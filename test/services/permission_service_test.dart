import 'package:flutter_test/flutter_test.dart';
import 'package:cartoon_camera/core/services/permission_service.dart';

void main() {
  group('PermissionService Tests', () {
    test('should have requestCameraPermission method', () {
      // 验证方法存在
      expect(PermissionService.requestCameraPermission, isA<Function>());
    });

    test('should have requestPhotosPermission method', () {
      // 验证方法存在
      expect(PermissionService.requestPhotosPermission, isA<Function>());
    });

    test('should have requestStoragePermission method', () {
      // 验证方法存在
      expect(PermissionService.requestStoragePermission, isA<Function>());
    });

    test('should have requestMultiplePermissions method', () {
      // 验证方法存在
      expect(PermissionService.requestMultiplePermissions, isA<Function>());
    });

    test('should have checkPermissionStatus method', () {
      // 验证方法存在
      expect(PermissionService.checkPermissionStatus, isA<Function>());
    });

    test('should have showPermissionDeniedMessage method', () {
      // 验证方法存在
      expect(PermissionService.showPermissionDeniedMessage, isA<Function>());
    });
  });
}

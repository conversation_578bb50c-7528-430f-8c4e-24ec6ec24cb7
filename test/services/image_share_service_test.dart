import 'package:flutter_test/flutter_test.dart';
import 'package:cartoon_camera/core/services/image_share_service.dart';

void main() {
  group('ImageShareService Tests', () {
    test('should have shareNetworkImage method', () {
      // 验证方法存在
      expect(ImageShareService.shareNetworkImage, isA<Function>());
    });

    test('should have shareText method', () {
      // 验证方法存在
      expect(ImageShareService.shareText, isA<Function>());
    });

    test('should have shareApp method', () {
      // 验证方法存在
      expect(ImageShareService.shareApp, isA<Function>());
    });
  });
}

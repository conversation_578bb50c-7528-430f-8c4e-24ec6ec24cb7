# 开发规范和规则

- 用户要求完全重构为AI头像生成应用，不要生成总结性文档、测试脚本，不要编译和运行
- 首页设计要求：最新上新部分有三张卡片通过接口获取(可用mock数据)，支持左右滑动，中间卡片大两边小且都有阴影；大分类(经典动漫、手绘插画等)通过接口获取数量不固定，每分类最多显示3张，点击全部跳转其他页面
- 分类展示区域不需要横向滚动，需要删除不需要的文件；不要生成总结性Markdown文档、测试脚本，不要编译和运行
- 友盟SDK页面采集模式已修改为自动采集：调用setPageCollectionModeAuto()，不再需要手动调用onPageStart/onPageEnd。BaseController和BasePage已简化，移除了手动页面统计逻辑。页面访问统计由友盟SDK自动处理，只保留自定义事件统计功能。
- 友盟统计事件已简化：移除了应用启动自动统计、页面访问详细统计、用户登录注册统计、照片生成分享保存统计等多余事件。只保留核心的VIP购买统计。页面统计由SDK自动采集处理，减少了代码复杂度和不必要的统计数据。
- VipService剩余次数管理已重构：移除本地剩余次数维护，改为通过/api/cartoon/transformed/count接口实时查询当月使用次数。VIP用户每月限制20次。canGenerate()和getRemainingGenerationsDescription()改为异步方法。相关调用处已更新为异步处理。
- iOS启动图配置：使用scaleAspectFill模式充满屏幕，约束设置为top/bottom/leading/trailing全屏显示
- 依赖注入规则：ApiService通过GetIt注册，应使用getIt<ApiService>()获取；其他GetX服务使用Get.find<>()获取
- 依赖注入已完全迁移到GetX Binding：移除了GetIt和Injectable，所有服务通过AppBinding统一管理，使用Get.find<>()获取服务实例
- 存储统一使用GetStorage：已将VipService中的SharedPreferences替换为StorageService（基于GetStorage），移除了shared_preferences依赖
- 代码质量改进：已修复所有编译错误和警告，将关键服务的print语句替换为Logger，剩余150个信息级别的print警告不影响功能
- UmengService依赖注入修复：在AppBinding中注册UmengService后，通过Future.microtask异步初始化，避免循环依赖问题
- 服务依赖注册顺序：AuthService -> OssService/VipService -> GenerationService/AppStartupService，确保被依赖的服务先注册
- VipService依赖注入修复：使用可空字段和懒加载getter避免late字段重复初始化错误，支持热重载
- Flutter资源路径规则：Image.asset路径不能以/开头，正确格式为"assets/images/filename.png"
- AppDataService和AppController职责分工：AppDataService专注静态数据管理（模板、产品信息、token），AppController负责动态状态管理（VIP状态、使用量、业务逻辑）。避免重复功能，保持单一职责原则。
- 已完成AppDataService到AppController的重构：AppController现在统一管理所有全局数据（模板数据、产品信息、VIP状态、使用量），AppDataService已删除。实现了真正的单一数据源管理，避免了静态vs动态数据的模糊分类。
- 用户要求移除TemplateService，HomeController直接使用AppController的模板数据，只在HomeController中处理UI逻辑（如列表只取3个）
- 用户要求移除HomeController中不必要的状态：selectedStyleType、selectedRecentIndex、isLoading，因为最新上新是内部维护的，选中风格后直接跳转不需要保存状态
- 用户要求移除AppController中的便捷方法(recommendTemplates、animeTemplates、illustrationTemplates)，只对外暴露templateData，在使用的地方通过templateData获取具体数据，避免不统一
- 用户要求移除AppController中所有便捷方法，只暴露完整数据，具体的便捷数据在对应页面的controller中获取
- 用户已经大幅简化AppController，移除了多余方法，添加了会员状态数据，要求继续移除其他便捷方法和属性，让它成为纯粹的数据管理器
- 用户要求移除VipService，支付使用ApplePaymentService，查询次数使用ApiService，移除AppController中VIP状态相关数据，只使用原始数据，在需要的页面自行处理
- 用户在AppController中增加了MembershipPeriod全局数据，需要补充load和refresh方法，并在初始化时加载
- 用户要求移除GenerationService，将生成逻辑放到generation页面自己的controller或service中
- 用户要求移除lib/features/result/result.dart文件，认为不需要这个导出文件

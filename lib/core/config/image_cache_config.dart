import 'package:flutter/painting.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';

/// 图片缓存配置
class ImageCacheConfig {
  /// 自定义缓存管理器
  static final CacheManager customCacheManager = CacheManager(
    Config(
      'custom_image_cache',
      stalePeriod: const Duration(days: 30), // 缓存7天
      maxNrOfCacheObjects: 500, // 最多缓存200个文件
      repo: JsonCacheInfoRepository(databaseName: 'custom_image_cache'),
      fileService: HttpFileService(),
    ),
  );

  /// 初始化图片缓存配置
  static void initialize() {
    // 设置全局图片缓存配置
    PaintingBinding.instance.imageCache.maximumSize = 100; // 内存中最多缓存100张图片
    PaintingBinding.instance.imageCache.maximumSizeBytes =
        50 << 20; // 内存缓存最大50MB
  }

  /// 清理缓存
  static Future<void> clearCache() async {
    await customCacheManager.emptyCache();
    PaintingBinding.instance.imageCache.clear();
    PaintingBinding.instance.imageCache.clearLiveImages();
  }

  /// 获取缓存大小
  static Future<int> getCacheSize() async {
    // 这里可以实现获取缓存大小的逻辑
    return 0;
  }

  /// 预加载图片
  static Future<void> preloadImage(String imageUrl) async {
    await customCacheManager.downloadFile(imageUrl);
  }

  /// 批量预加载图片
  static Future<void> preloadImages(List<String> imageUrls) async {
    final futures = imageUrls.map((url) => preloadImage(url));
    await Future.wait(futures);
  }
}

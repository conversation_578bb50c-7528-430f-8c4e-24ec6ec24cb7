import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:logger/logger.dart';

/// 认证服务
/// 负责用户登录、Token管理等
/// 直接使用GetStorage获取authorization
class AuthService extends GetxService {
  final Logger _logger = Logger();
  final GetStorage _storage = GetStorage();
  static const String tokenKey = 'token';

  /// 保存Token
  Future<void> saveToken(String newToken) async {
    try {
      _logger.d('准备保存Token: ${newToken.substring(0, 20)}...');
      // 直接使用GetStorage保存token
      await _storage.write(tokenKey, newToken);
      _logger.d('Token保存成功');

      // 立即验证保存结果
      final savedToken = _storage.read(tokenKey);
      _logger.d('验证保存结果: ${savedToken != null ? "成功" : "失败"}');
    } catch (e) {
      _logger.e('保存Token失败: $e');
    }
  }

  /// 直接从GetStorage获取Token
  String getTokenFromStorage() {
    try {
      final savedToken = _storage.read(tokenKey);
      _logger.d('从存储读取Token: ${savedToken != null ? "找到Token" : "Token为空"}');
      return savedToken ?? '';
    } catch (e) {
      _logger.e('从存储获取Token失败: $e');
      return '';
    }
  }
}

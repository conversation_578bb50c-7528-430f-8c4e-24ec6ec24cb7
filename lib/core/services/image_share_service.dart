import 'dart:io';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:get/get.dart' as getx;
import 'package:flutter/material.dart';

/// 图片分享服务
/// 负责下载网络图片并分享
class ImageShareService {
  static final Dio _dio = Dio();

  /// 分享网络图片
  /// [imageUrl] 图片URL
  /// [text] 分享文本
  /// [subject] 分享主题
  /// [showLoading] 是否显示加载提示
  static Future<bool> shareNetworkImage(
    String imageUrl, {
    String? text,
    String? subject,
    bool showLoading = true,
  }) async {
    try {
      if (showLoading) {
        getx.Get.dialog(
          const Center(child: CircularProgressIndicator()),
          barrierDismissible: false,
        );
      }

      // 下载图片到临时文件
      final imageFile = await _downloadImage(imageUrl);
      if (imageFile == null) {
        throw Exception('图片下载失败');
      }

      if (showLoading) {
        getx.Get.back(); // 关闭加载对话框
      }

      // 分享图片
      final result = await Share.shareXFiles(
        [XFile(imageFile.path)],
        text: text ?? '来自卡通相机的精彩作品',
        subject: subject ?? '卡通相机 - AI照片风格重绘',
      );

      // 删除临时文件
      await imageFile.delete();

      // 检查分享结果
      if (result.status == ShareResultStatus.success) {
        getx.Get.snackbar(
          '分享成功',
          '图片已成功分享',
          snackPosition: getx.SnackPosition.BOTTOM,
          backgroundColor: const Color(0xFF4CAF50),
          colorText: Colors.white,
          duration: const Duration(seconds: 2),
        );
        return true;
      } else if (result.status == ShareResultStatus.dismissed) {
        // 用户取消分享，不显示错误信息
        return false;
      } else {
        throw Exception('分享失败');
      }
    } catch (e) {
      if (showLoading && getx.Get.isDialogOpen == true) {
        getx.Get.back(); // 关闭加载对话框
      }

      getx.Get.snackbar(
        '分享失败',
        '分享图片时出现错误：${e.toString()}',
        snackPosition: getx.SnackPosition.BOTTOM,
        backgroundColor: const Color(0xFFFF5722),
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );

      print('分享图片失败: $e');
      return false;
    }
  }

  /// 分享文本
  /// [text] 分享的文本内容
  /// [subject] 分享主题
  static Future<bool> shareText(String text, {String? subject}) async {
    try {
      await Share.share(text, subject: subject ?? '卡通相机');
      return true;
    } catch (e) {
      getx.Get.snackbar(
        '分享失败',
        '分享时出现错误：${e.toString()}',
        snackPosition: getx.SnackPosition.BOTTOM,
        backgroundColor: const Color(0xFFFF5722),
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );

      print('分享文本失败: $e');
      return false;
    }
  }

  /// 分享应用App Store链接
  static Future<bool> shareApp() async {
    try {
      // TODO: 请将下面的链接替换为实际的App Store链接
      // 格式：https://apps.apple.com/app/id[实际的APP_ID]
      const appStoreUrl = 'https://apps.apple.com/search?term=卡通相机';

      // 使用shareUri直接分享链接，用户可以直接点击打开
      await Share.shareUri(
        Uri.parse(appStoreUrl),
        // 可选：添加分享主题
        // subject: '卡通相机 - AI照片风格重绘',
      );

      return true;
    } catch (e) {
      print('分享App Store链接失败，降级到文本分享: $e');

      // 降级方案：分享文本链接
      const fallbackText = '''🎨 卡通相机 - AI照片风格重绘
📱 立即下载：https://apps.apple.com/search?term=卡通相机''';

      return await shareText(fallbackText, subject: '推荐一个超棒的AI照片处理应用');
    }
  }

  /// 下载网络图片到临时文件
  static Future<File?> _downloadImage(String imageUrl) async {
    try {
      final response = await _dio.get(
        imageUrl,
        options: Options(responseType: ResponseType.bytes),
      );

      if (response.statusCode == 200) {
        final bytes = response.data as Uint8List;
        final tempDir = await getTemporaryDirectory();
        final fileName =
            'share_image_${DateTime.now().millisecondsSinceEpoch}.jpg';
        final file = File('${tempDir.path}/$fileName');
        await file.writeAsBytes(bytes);
        return file;
      }
      return null;
    } catch (e) {
      print('下载图片失败: $e');
      return null;
    }
  }
}

import 'dart:io';
import 'package:permission_handler/permission_handler.dart';
import 'package:get/get.dart' as getx;
import 'package:logger/logger.dart';
import 'package:flutter/foundation.dart';

/// 统一权限管理服务 - 精简版
/// 使用系统原生权限提示，减少自定义弹窗
class PermissionService {
  static final Logger _logger = Logger();

  /// 检查并请求相机权限
  static Future<bool> requestCameraPermission() async {
    try {
      // 检查是否在模拟器中运行
      if (kDebugMode && await _isSimulator()) {
        if (Platform.isIOS) {
          print('检测到iOS模拟器环境，相机功能不可用');
          _showSimpleMessage('模拟器环境：相机功能不可用，请在真机上测试');
          return false; // iOS模拟器中相机功能不可用
        } else if (Platform.isAndroid) {
          print('检测到Android模拟器环境，跳过相机权限检查');
          _showSimpleMessage('模拟器环境：相机权限检查已跳过');
          return true; // Android模拟器中允许相机功能
        }
      }

      final status = await Permission.camera.status;

      if (status.isGranted) {
        return true;
      }

      // 直接请求权限，让系统处理提示
      if (status.isDenied) {
        final result = await Permission.camera.request();
        return result.isGranted;
      }

      // 权限被永久拒绝，只显示简单提示
      if (status.isPermanentlyDenied) {
        _showSimpleMessage('相机权限被拒绝，请在设置中手动开启');
        return false;
      }

      return false;
    } catch (e) {
      _logger.e('请求相机权限失败: $e');
      // 在模拟器环境下，权限检查失败时返回true
      if (kDebugMode && await _isSimulator()) {
        print('模拟器环境下权限检查失败，允许继续: $e');
        return Platform.isAndroid; // Android模拟器返回true，iOS返回false
      }
      return false;
    }
  }

  /// 检查并请求相册权限
  static Future<bool> requestPhotosPermission() async {
    print('检查并请求相册权限');
    try {
      // 检查是否在模拟器中运行
      if (kDebugMode && await _isSimulator()) {
        print('检测到模拟器环境，跳过相册权限检查');
        _showSimpleMessage('模拟器环境：相册功能可用，权限检查已跳过');
        return true; // 在模拟器中直接返回true
      }

      final status = await Permission.photos.status;
      print('相册权限status: $status');
      if (status.isGranted) {
        return true;
      }

      // 直接请求权限，让系统处理提示
      if (status.isDenied) {
        print('相册权限isDenied');
        final result = await Permission.photos.request();
        print('相册权限result: $result');
        return result.isGranted;
      }

      // 权限被永久拒绝，只显示简单提示
      if (status.isPermanentlyDenied) {
        _showSimpleMessage('相册权限被拒绝，请在设置中手动开启');
        return false;
      }

      return false;
    } catch (e) {
      _logger.e('请求相册权限失败: $e');
      // 在模拟器环境下，权限检查失败时返回true
      if (kDebugMode && await _isSimulator()) {
        print('模拟器环境下相册权限检查失败，允许继续: $e');
        return true;
      }
      return false;
    }
  }

  /// 检测是否在模拟器中运行
  static Future<bool> _isSimulator() async {
    try {
      // 在调试模式下，简化检测逻辑
      if (kDebugMode) {
        print('调试模式下，假设为模拟器环境');
        return true;
      }

      if (Platform.isIOS) {
        // iOS模拟器检测
        return Platform.environment.containsKey('SIMULATOR_DEVICE_NAME') ||
            Platform.environment.containsKey('FLUTTER_TEST');
      } else if (Platform.isAndroid) {
        // Android模拟器检测
        return Platform.environment.containsKey('ANDROID_EMULATOR') ||
            Platform.environment.containsKey('FLUTTER_TEST');
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// 检查并请求存储权限（Android）
  static Future<bool> requestStoragePermission() async {
    if (!Platform.isAndroid) {
      return true; // iOS不需要存储权限
    }

    try {
      // 简化权限请求，直接使用photos权限（适用于Android 13+）
      final status = await Permission.photos.status;

      if (status.isGranted) {
        return true;
      }

      // 直接请求权限，让系统处理提示
      if (status.isDenied) {
        final result = await Permission.photos.request();
        return result.isGranted;
      }

      // 权限被永久拒绝，只显示简单提示
      if (status.isPermanentlyDenied) {
        _showSimpleMessage('存储权限被拒绝，请在设置中手动开启');
        return false;
      }

      return false;
    } catch (e) {
      _logger.e('请求存储权限失败: $e');
      return false;
    }
  }

  /// 批量请求权限
  static Future<Map<Permission, PermissionStatus>> requestMultiplePermissions(
    List<Permission> permissions,
  ) async {
    try {
      return await permissions.request();
    } catch (e) {
      _logger.e('批量请求权限失败: $e');
      return {};
    }
  }

  /// 检查权限状态
  static Future<PermissionStatus> checkPermissionStatus(
    Permission permission,
  ) async {
    try {
      return await permission.status;
    } catch (e) {
      _logger.e('检查权限状态失败: $e');
      return PermissionStatus.denied;
    }
  }

  /// 显示简单的权限提示消息
  static void _showSimpleMessage(String message) {
    getx.Get.snackbar(
      '权限提示',
      message,
      snackPosition: getx.SnackPosition.BOTTOM,
      duration: const Duration(seconds: 3),
    );
  }

  /// 显示权限被拒绝的提示（保持向后兼容）
  static void showPermissionDeniedMessage(String permissionName) {
    _showSimpleMessage('需要$permissionName权限才能使用此功能');
  }
}

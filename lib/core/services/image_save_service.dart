import 'dart:io';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:gallery_saver/gallery_saver.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:get/get.dart' as getx;
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

/// 图片保存服务
/// 负责下载网络图片并保存到相册
class ImageSaveService {
  static final Dio _dio = Dio();

  /// 保存网络图片到相册
  /// [imageUrl] 图片URL
  /// [showLoading] 是否显示加载提示
  /// [showSuccess] 是否显示成功提示
  /// [showError] 是否显示错误提示
  static Future<bool> saveNetworkImageToGallery(
    String imageUrl, {
    bool showLoading = true,
    bool showSuccess = true,
    bool showError = true,
  }) async {
    try {
      // 检查权限
      final hasPermission = await _checkAndRequestPermissions();
      if (!hasPermission) {
        return false; // 权限检查方法内部已处理提示
      }

      if (showLoading) {
        getx.Get.dialog(
          const Center(child: CircularProgressIndicator()),
          barrierDismissible: false,
        );
      }

      // 下载图片
      final imageFile = await _downloadImage(imageUrl);
      if (imageFile == null) {
        throw Exception('图片下载失败');
      }

      // 保存到相册
      final result = await GallerySaver.saveImage(
        imageFile.path,
        albumName: '卡通相机',
      );

      // 删除临时文件
      await imageFile.delete();

      if (showLoading) {
        getx.Get.back(); // 关闭加载对话框
      }

      if (result == true) {
        if (showSuccess) {
          getx.Get.snackbar(
            '保存成功',
            '图片已保存到相册',
            snackPosition: getx.SnackPosition.BOTTOM,
            backgroundColor: const Color(0xFF4CAF50),
            colorText: Colors.white,
            duration: const Duration(seconds: 2),
          );
        }
        return true;
      } else {
        throw Exception('保存到相册失败');
      }
    } catch (e) {
      if (showLoading && getx.Get.isDialogOpen == true) {
        getx.Get.back(); // 关闭加载对话框
      }

      if (showError) {
        getx.Get.snackbar(
          '保存失败',
          '保存图片时出现错误：${e.toString()}',
          snackPosition: getx.SnackPosition.BOTTOM,
          backgroundColor: const Color(0xFFFF5722),
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );
      }

      print('保存图片失败: $e');
      return false;
    }
  }

  /// 下载网络图片到临时文件
  static Future<File?> _downloadImage(String imageUrl) async {
    try {
      final response = await _dio.get(
        imageUrl,
        options: Options(responseType: ResponseType.bytes),
      );

      if (response.statusCode == 200) {
        final bytes = response.data as Uint8List;
        final tempDir = await getTemporaryDirectory();
        final fileName =
            'temp_image_${DateTime.now().millisecondsSinceEpoch}.jpg';
        final file = File('${tempDir.path}/$fileName');
        await file.writeAsBytes(bytes);
        return file;
      }
      return null;
    } catch (e) {
      print('下载图片失败: $e');
      return null;
    }
  }

  /// 检查并请求相册权限
  static Future<bool> _checkAndRequestPermissions() async {
    print('开始检查相册权限...');
    print('当前平台: ${Platform.operatingSystem}');
    print('是否为调试模式: $kDebugMode');
    print('是否为模拟器: ${_isRunningOnSimulator()}');

    // 在模拟器环境下跳过权限检查
    if (kDebugMode && _isRunningOnSimulator()) {
      print('检测到模拟器环境，跳过权限检查');
      return true;
    }

    // 根据平台检查不同权限
    if (Platform.isAndroid) {
      print('执行Android权限检查');
      return await _checkAndroidPermissions();
    } else if (Platform.isIOS) {
      print('执行iOS权限检查');
      return await _checkIOSPermissions();
    }
    print('未知平台，权限检查失败');
    return false;
  }

  /// 检测是否在模拟器上运行
  static bool _isRunningOnSimulator() {
    // 在调试模式下，简化检测逻辑
    if (kDebugMode) {
      print('调试模式下，假设为模拟器环境');
      return true;
    }

    if (Platform.isIOS) {
      // iOS模拟器检测
      return Platform.environment.containsKey('SIMULATOR_DEVICE_NAME') ||
          Platform.environment.containsKey('FLUTTER_TEST');
    } else if (Platform.isAndroid) {
      // Android模拟器检测
      return Platform.environment.containsKey('ANDROID_EMULATOR') ||
          Platform.environment.containsKey('FLUTTER_TEST');
    }
    return false;
  }

  /// 检查Android权限
  static Future<bool> _checkAndroidPermissions() async {
    try {
      // Android 13+ 使用新的媒体权限
      if (await _isAndroid13OrHigher()) {
        final status = await Permission.photos.status;
        if (status.isGranted) {
          return true;
        } else if (status.isDenied) {
          // 直接请求权限
          final result = await Permission.photos.request();
          if (result.isGranted) {
            return true;
          } else if (result.isPermanentlyDenied) {
            await _showPermissionDialog();
          }
          return false;
        } else if (status.isPermanentlyDenied) {
          await _showPermissionDialog();
          return false;
        }
      } else {
        // Android 12及以下使用存储权限
        final status = await Permission.storage.status;
        if (status.isGranted) {
          return true;
        } else if (status.isDenied) {
          // 直接请求权限
          final result = await Permission.storage.request();
          if (result.isGranted) {
            return true;
          } else if (result.isPermanentlyDenied) {
            await _showPermissionDialog();
          }
          return false;
        } else if (status.isPermanentlyDenied) {
          await _showPermissionDialog();
          return false;
        }
      }
      return false;
    } catch (e) {
      // 权限检查失败时，在模拟器环境下返回true
      if (kDebugMode && _isRunningOnSimulator()) {
        print('Android权限检查失败，模拟器环境下允许继续: $e');
        return true;
      }
      print('Android权限检查失败: $e');
      return false;
    }
  }

  /// 检查iOS权限
  static Future<bool> _checkIOSPermissions() async {
    try {
      final status = await Permission.photos.status;
      if (status.isGranted) {
        return true;
      } else if (status.isDenied) {
        // 直接请求权限
        final result = await Permission.photos.request();
        if (result.isGranted) {
          return true;
        } else if (result.isPermanentlyDenied) {
          await _showPermissionDialog();
        }
        return false;
      } else if (status.isPermanentlyDenied) {
        await _showPermissionDialog();
        return false;
      }
      return false;
    } catch (e) {
      // 权限检查失败时，在模拟器环境下返回true
      if (kDebugMode && _isRunningOnSimulator()) {
        print('iOS权限检查失败，模拟器环境下允许继续: $e');
        return true;
      }
      print('iOS权限检查失败: $e');
      return false;
    }
  }

  /// 检查是否为Android 13或更高版本
  static Future<bool> _isAndroid13OrHigher() async {
    if (Platform.isAndroid) {
      // 这里可以使用device_info_plus获取更准确的版本信息
      // 暂时返回false，使用传统权限
      return false;
    }
    return false;
  }

  /// 显示权限引导对话框
  static Future<void> _showPermissionDialog() async {
    await getx.Get.dialog(
      AlertDialog(
        title: const Text('需要相册权限'),
        content: const Text('为了保存图片到相册，需要您在设置中开启相册访问权限。'),
        actions: [
          TextButton(onPressed: () => getx.Get.back(), child: const Text('取消')),
          TextButton(
            onPressed: () {
              getx.Get.back();
              openAppSettings();
            },
            child: const Text('去设置'),
          ),
        ],
      ),
    );
  }
}

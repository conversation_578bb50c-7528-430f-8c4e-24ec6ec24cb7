import 'dart:io';
import 'package:get/get.dart';
import 'package:path/path.dart' as path;
import 'package:flutter_oss_aliyun/flutter_oss_aliyun.dart';
import 'package:logger/logger.dart';
import '../../services/api_service.dart';
import '../../shared/models/template_response_model.dart';

/// OSS上传服务 - 精简版
/// 只对外暴露初始化和上传功能
class OssService extends GetxService {
  late final ApiService _apiService;
  final Logger _logger = Logger();

  // OSS配置信息和STS凭证
  OSSConfigData? _ossConfig;
  STSInfoData? _stsInfo;
  DateTime? _stsObtainTime;
  Client? _ossClient;

  @override
  void onInit() {
    super.onInit();
    _apiService = Get.find<ApiService>();
  }

  /// 初始化OSS配置
  Future<bool> initialize() async {
    try {
      // 从ApiService获取OSS配置信息
      _ossConfig = await _apiService.getOssConfig();

      // 从ApiService获取STS临时凭证
      _stsInfo = await _apiService.getStsInfo();
      _stsObtainTime = DateTime.now();

      // 重置OSS Client以使用新配置
      _ossClient = null;

      return true;
    } catch (e) {
      _logger.e('初始化OSS失败: $e');
      return false;
    }
  }

  /// 上传图片到OSS
  Future<String?> uploadImage(File imageFile) async {
    try {
      // 确保OSS已初始化
      if (!_isInitialized()) {
        final success = await initialize();
        if (!success) return null;
      }

      // 检查STS凭证是否需要刷新
      if (_needsRefreshSts()) {
        await _refreshSts();
      }

      // 生成文件名和对象键
      final fileName = _generateFileName(imageFile);
      final objectKey = 'temporary-photos/$fileName';

      _logger.d(
        'OSS配置: bucket=${_ossConfig!.bucket}, endpoint=${_ossConfig!.endpoint}',
      );
      _logger.d('上传文件: $fileName, 对象键: $objectKey');
      _logger.d(
        'STS信息: accessKeyId=${_stsInfo!.accessKeyId.substring(0, 10)}...',
      );

      // 创建OSS客户端并上传
      final client = _createOssClient();
      final response = await client.putObject(
        imageFile.readAsBytesSync(),
        objectKey,
        option: PutRequestOption(
          override: false,
          aclModel: AclMode.inherited,
          storageType: StorageType.standard,
        ),
      );

      _logger.d('OSS上传响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final cdnUrl = '${_ossConfig!.cdnHost}/$objectKey';
        _logger.d('OSS上传成功，CDN地址: $cdnUrl');
        return cdnUrl;
      }

      _logger.w('OSS上传失败，状态码: ${response.statusCode}');
      return null;
    } catch (e) {
      _logger.e('上传失败: $e');
      return null;
    }
  }

  /// 检查是否已初始化
  bool _isInitialized() {
    return _ossConfig != null && _stsInfo != null;
  }

  /// 检查是否需要刷新STS凭证
  bool _needsRefreshSts() {
    if (_stsObtainTime == null) return true;

    final now = DateTime.now();
    final timeSinceObtain = now.difference(_stsObtainTime!);
    // STS凭证通常1小时过期，提前5分钟刷新
    return timeSinceObtain > const Duration(minutes: 55);
  }

  /// 刷新STS凭证
  Future<void> _refreshSts() async {
    try {
      _stsInfo = await _apiService.getStsInfo();
      _stsObtainTime = DateTime.now();
      _ossClient = null; // 重置客户端以使用新凭证
    } catch (e) {
      _logger.e('刷新STS凭证失败: $e');
      rethrow;
    }
  }

  /// 创建OSS客户端
  Client _createOssClient() {
    // 构建OSS endpoint域名（不包含协议）
    final endpointDomain = _buildFullEndpoint(_ossConfig!.endpoint);
    _logger.d('OSS endpoint域名: $endpointDomain');
    _logger.d('OSS bucket: ${_ossConfig!.bucket}');

    return _ossClient ??= Client.init(
      ossEndpoint: endpointDomain,
      bucketName: _ossConfig!.bucket,
      authGetter: () async {
        _logger.d('获取OSS认证信息');
        return Auth(
          accessKey: _stsInfo!.accessKeyId,
          accessSecret: _stsInfo!.accessKeySecret,
          expire: _stsInfo!.expiration,
          secureToken: _stsInfo!.securityToken,
        );
      },
    );
  }

  /// 构建OSS endpoint（不包含协议）
  String _buildFullEndpoint(String endpoint) {
    // 移除协议前缀，OSS插件会自动添加
    if (endpoint.startsWith('http://')) {
      endpoint = endpoint.substring(7);
    } else if (endpoint.startsWith('https://')) {
      endpoint = endpoint.substring(8);
    }

    // 如果只是区域信息，构建完整的OSS endpoint域名
    if (endpoint.contains('oss-') && !endpoint.contains('.aliyuncs.com')) {
      return '$endpoint.aliyuncs.com';
    }

    // 其他情况，直接返回域名
    return endpoint;
  }

  /// 生成文件名
  String _generateFileName(File file) {
    final extension = path.extension(file.path);
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = DateTime.now().microsecond;
    return 'upload_${timestamp}_$random$extension';
  }

  /// 检查OSS是否已初始化
  bool get isInitialized => _isInitialized();
}

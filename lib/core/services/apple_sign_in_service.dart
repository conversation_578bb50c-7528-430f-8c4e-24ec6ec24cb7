import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

/// 苹果登录服务
class AppleSignInService {
  String? _nonce;
  String get nonce => _nonce ?? '';
  AppleSignInService() {
    final rawNonce = _generateNonce();
    final nonce = _sha256ofString(rawNonce);
    _nonce = nonce;
  }

  /// 执行苹果登录
  Future<AuthorizationCredentialAppleID> signIn() async {
    if (!await SignInWithApple.isAvailable()) {
      throw Exception('苹果登录在当前设备上不可用');
    }

    try {
      return await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
        nonce: nonce,
      );
    } on SignInWithAppleAuthorizationException catch (e) {
      throw _handleAuthorizationException(e);
    }
  }

  /// 生成随机nonce字符串
  String _generateNonce([int length = 32]) {
    const charset =
        '0123456789ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvwxyz-._';
    final random = Random.secure();
    return List.generate(
      length,
      (_) => charset[random.nextInt(charset.length)],
    ).join();
  }

  /// 计算字符串的SHA256哈希值
  String _sha256ofString(String input) {
    final bytes = utf8.encode(input);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// 处理苹果登录授权异常
  Exception _handleAuthorizationException(
    SignInWithAppleAuthorizationException e,
  ) {
    switch (e.code) {
      case AuthorizationErrorCode.canceled:
        return Exception('用户取消了登录');
      case AuthorizationErrorCode.failed:
        return Exception('登录失败，请重试');
      case AuthorizationErrorCode.invalidResponse:
        return Exception('收到无效的登录响应');
      case AuthorizationErrorCode.notHandled:
        return Exception('登录请求未被处理');
      case AuthorizationErrorCode.notInteractive:
        return Exception('无法显示登录界面');
      case AuthorizationErrorCode.unknown:
        return Exception('登录过程中出现未知错误');
    }
  }
}

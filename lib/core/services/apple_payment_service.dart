import 'dart:async';
import 'package:cartoon_camera/controllers/app_controller.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';

/// 苹果支付服务
class ApplePaymentService extends GetxService {
  static final ApplePaymentService _instance = ApplePaymentService._internal();
  factory ApplePaymentService() => _instance;
  ApplePaymentService._internal();

  final Logger _logger = Logger();

  final InAppPurchase _inAppPurchase = InAppPurchase.instance;

  List<ProductDetails> _products = [];
  bool _initialized = false;

  /// 初始化支付服务
  Future<void> _ensureInitialized() async {
    if (_initialized) return;

    if (!await _inAppPurchase.isAvailable()) {
      throw Exception('设备不支持内购功能');
    }

    final appController = Get.find<AppController>();

    // 等待产品信息加载完成
    _logger.d('等待产品信息加载完成...');
    await _waitForProductInfo(appController);

    final Set<String> ids =
        appController.productInfo.value
            ?.map((e) => e.productIdentifier)
            .toSet() ??
        <String>{};

    _logger.d('商品id: $ids');

    if (ids.isEmpty) {
      throw Exception('没有可用的产品ID');
    }

    final ProductDetailsResponse response = await _inAppPurchase
        .queryProductDetails(ids);

    if (response.notFoundIDs.isNotEmpty) {
      throw Exception('未找到产品ID: ${response.notFoundIDs}');
    }

    // debug
    _logger.d(
      '商品查询结果: 成功${response.productDetails.length}个, 失败${response.notFoundIDs.length}个',
    );
    if (response.productDetails.isNotEmpty) {
      for (var product in response.productDetails) {
        _logger.d(
          '产品: ${product.id} | ${product.title} | ${product.price}| ${product.description}',
        );
      }
    }
    if (response.notFoundIDs.isNotEmpty) {
      _logger.d('未找到的产品ID: ${response.notFoundIDs}');
    }
    if (response.error != null) {
      _logger.d('查询错误: ${response.error}');
    }

    // debug
    _products = response.productDetails;
    _initialized = true;
    _logger.d('支付服务初始化完成，共加载 ${_products.length} 个产品');
  }

  /// 等待产品信息加载完成
  Future<void> _waitForProductInfo(AppController appController) async {
    // 如果产品信息已经加载完成，直接返回
    if (appController.productInfo.value != null) {
      return;
    }

    // 等待产品信息加载完成，最多等待10秒
    final completer = Completer<void>();
    late StreamSubscription subscription;

    subscription = appController.productInfo.listen((productInfo) {
      if (productInfo != null) {
        subscription.cancel();
        completer.complete();
      }
    });

    // 设置超时
    Timer(const Duration(seconds: 10), () {
      if (!completer.isCompleted) {
        subscription.cancel();
        completer.completeError('等待产品信息超时');
      }
    });

    await completer.future;
  }

  /// 购买会员套餐
  Future<bool> purchaseMembership(String productId) async {
    try {
      await _ensureInitialized();
      _logger.d('购买会员套餐-初始化完成: $productId');

      // 检查设备是否支持内购
      final available = await _inAppPurchase.isAvailable();
      if (!available) {
        throw Exception('设备不支持内购功能');
      }

      // 注意：不在购买前调用 restorePurchases，避免干扰购买流程
      // 查找产品
      final product = _products.firstWhere(
        (p) => p.id == productId,
        orElse: () => throw Exception('未找到产品: $productId'),
      );

      _logger.d('找到产品: ${product.id}, 价格: ${product.price}');

      // 发起购买
      final purchaseParam = PurchaseParam(productDetails: product);
      final buyResult = await _inAppPurchase.buyNonConsumable(
        purchaseParam: purchaseParam,
      );

      return buyResult;
    } catch (e) {
      _logger.e('购买过程中出错: $e');
      rethrow;
    }
  }

  /// 恢复购买记录
  /// 注意：调用后需要监听 purchaseStream 来接收结果
  /// 这个方法应该由用户主动触发（比如点击"恢复购买"按钮）
  Future<void> restorePurchases() async {
    await _ensureInitialized();
    _logger.d('用户主动恢复购买');
    try {
      await _inAppPurchase.restorePurchases();
      _logger.d('恢复购买请求已发送，等待结果...');
    } catch (e) {
      _logger.e('恢复购买失败: $e');
      if (e.toString().contains('Finance Authentication Error')) {
        throw Exception('请在设备设置中登录沙盒测试账号后重试');
      }
      rethrow;
    }
  }

  /// 查询用户会员的有效期
  /// 返回会员的开始时间和结束时间（毫秒时间戳）
  /// 注意：这个方法主要用于应用启动时的状态检查，不会主动恢复购买
  Future<MembershipPeriod?> getMembershipPeriod({
    bool isAfterPurchase = false,
  }) async {
    await _ensureInitialized();

    try {
      _logger.d('开始查询会员状态... (购买后查询: $isAfterPurchase)');

      if (isAfterPurchase) {
        // 购买后查询：等待一下让服务器同步，然后监听更多事件
        await Future.delayed(const Duration(milliseconds: 500));

        // 监听购买流中的多个事件，直到找到有效购买或超时
        final completer = Completer<MembershipPeriod?>();
        late StreamSubscription subscription;

        subscription = _inAppPurchase.purchaseStream.listen((purchases) {
          _logger.d('收到购买事件，包含 ${purchases.length} 个购买记录');
          final activePurchase = _findActivePurchase(purchases);
          if (activePurchase != null) {
            _logger.d(
              '购买后查询成功找到会员记录: ${activePurchase.productID} | ${activePurchase.status}',
            );
            subscription.cancel();
            completer.complete(_buildMembershipPeriod(activePurchase));
          }
        });

        // 设置超时
        Timer(const Duration(seconds: 5), () {
          if (!completer.isCompleted) {
            subscription.cancel();
            _logger.w('购买后查询会员状态超时');
            completer.complete(null);
          }
        });

        // 触发恢复购买来获取最新状态
        await _inAppPurchase.restorePurchases();

        return await completer.future;
      } else {
        // 常规查询：静默恢复购买记录
        await _inAppPurchase.restorePurchases();

        // 等待购买流返回结果
        await for (final purchases in _inAppPurchase.purchaseStream.take(1)) {
          final activePurchase = _findActivePurchase(purchases);
          if (activePurchase != null) {
            _logger.d(
              '找到有效的会员购买记录: ${activePurchase.productID} | ${activePurchase.status}',
            );
            return _buildMembershipPeriod(activePurchase);
          }
        }

        _logger.d('未找到有效的会员购买记录');
        return null; // 没有有效的会员
      }
    } catch (e) {
      _logger.e('查询会员信息失败: $e');

      // 检查是否是认证错误
      if (e.toString().contains('Finance Authentication Error') ||
          e.toString().contains('AMSStatusCode=401')) {
        _logger.w('检测到沙盒认证错误，可能需要在设备设置中登录沙盒测试账号');
        // 返回 null 而不是抛出异常，让应用继续运行
        return null;
      }

      throw Exception('查询会员信息失败: $e');
    }
  }

  /// 查找有效的购买记录
  PurchaseDetails? _findActivePurchase(List<PurchaseDetails> purchases) {
    final productIds = _products.map((p) => p.id).toSet();

    _logger.d('查找有效购买记录:');
    _logger.d('- 可用产品ID: $productIds');
    _logger.d('- 收到购买记录数量: ${purchases.length}');

    for (var purchase in purchases) {
      _logger.d(
        '- 购买记录: ${purchase.productID} | 状态: ${purchase.status} | 时间: ${purchase.transactionDate}',
      );
    }

    final validPurchases = purchases
        .where(
          (purchase) =>
              (purchase.status == PurchaseStatus.purchased ||
                  purchase.status == PurchaseStatus.restored) &&
              productIds.contains(purchase.productID),
        )
        .toList();

    _logger.d('- 有效购买记录数量: ${validPurchases.length}');
    if (validPurchases.isNotEmpty) {
      for (var purchase in validPurchases) {
        _logger.d('- 有效购买: ${purchase.productID} | 状态: ${purchase.status}');
      }
    }
    if (validPurchases.isEmpty) return null;

    // 按购买时间排序，返回最新的
    validPurchases.sort((a, b) {
      final dateA =
          DateTime.tryParse(b.transactionDate ?? '') ?? DateTime(1970);
      final dateB =
          DateTime.tryParse(a.transactionDate ?? '') ?? DateTime(1970);
      return dateA.compareTo(dateB);
    });

    return validPurchases.first;
  }

  /// 构建会员有效期信息
  MembershipPeriod _buildMembershipPeriod(PurchaseDetails purchase) {
    final purchaseDate =
        DateTime.tryParse(purchase.transactionDate ?? '') ?? DateTime.now();
    final membershipType = _getMembershipType(purchase.productID);
    final endTime = _calculateEndTime(purchaseDate, membershipType);

    return MembershipPeriod(
      startTime: purchaseDate.millisecondsSinceEpoch,
      endTime: endTime.millisecondsSinceEpoch,
      productId: purchase.productID,
      membershipType: membershipType,
      isExpired: DateTime.now().isAfter(endTime),
    );
  }

  /// 获取会员类型
  MembershipType _getMembershipType(String productId) {
    switch (productId) {
      case 'cartoon_week':
        return MembershipType.week;
      case 'cartoon_month':
        return MembershipType.month;
      case 'cartoon_year':
        return MembershipType.year;
      default:
        return MembershipType.week;
    }
  }

  /// 计算会员结束时间
  DateTime _calculateEndTime(DateTime startTime, MembershipType type) {
    switch (type) {
      case MembershipType.week:
        return startTime.add(const Duration(days: 7));
      case MembershipType.month:
        return DateTime(startTime.year, startTime.month + 1, startTime.day);
      case MembershipType.year:
        return DateTime(startTime.year + 1, startTime.month, startTime.day);
    }
  }
}

/// 会员有效期信息
class MembershipPeriod {
  final int startTime; // 开始时间（毫秒时间戳）
  final int endTime; // 结束时间（毫秒时间戳）
  final String productId; // 产品ID
  final MembershipType membershipType; // 会员类型
  final bool isExpired; // 是否已过期

  MembershipPeriod({
    required this.startTime,
    required this.endTime,
    required this.productId,
    required this.membershipType,
    required this.isExpired,
  });

  /// 剩余天数
  int get remainingDays {
    if (isExpired) return 0;
    final remaining = endTime - DateTime.now().millisecondsSinceEpoch;
    return (remaining / (1000 * 60 * 60 * 24)).ceil();
  }

  @override
  String toString() {
    return 'MembershipPeriod(startTime: $startTime, endTime: $endTime, productId: $productId, type: $membershipType, expired: $isExpired)';
  }
}

/// 会员类型
enum MembershipType { week, month, year }

import 'dart:io';
import 'package:get/get.dart';
import 'package:umeng_common_sdk/umeng_common_sdk.dart';

/// 友盟统计服务
class UmengService extends GetxService {
  static final UmengService _instance = UmengService._internal();
  factory UmengService() => _instance;
  UmengService._internal();

  static const String _appKey = '688ae4356c255c772811b92d';
  bool _isInitialized = false;

  @override
  void onInit() {
    super.onInit();
    initialize();
  }

  /// 初始化友盟SDK
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await UmengCommonSdk.initCommon(_appKey, _appKey, _getChannel());
      UmengCommonSdk.setPageCollectionModeAuto();
      _isInitialized = true;
    } catch (e) {
      // 初始化失败不影响应用运行
    }
  }

  /// 获取渠道名称
  String _getChannel() {
    return Platform.isIOS ? 'App Store' : 'default';
  }

  /// 手动上报事件
  Future<void> trackEvent(
    String eventId, [
    Map<String, String>? parameters,
  ]) async {
    if (!_isInitialized) return;

    try {
      UmengCommonSdk.onEvent(eventId, parameters ?? <String, dynamic>{});
    } catch (e) {
      // 上报失败不影响应用运行
    }
  }
}

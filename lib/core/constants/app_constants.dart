/// 应用常量配置
class AppConstants {
  // 应用信息
  static const String appName = 'AI头像生成器';
  static const String appVersion = '1.0.0';

  // API配置
  static const String baseUrl = 'https://api.cloudfare.com.cn'; // 你的新baseUrl
  static const int connectTimeout = 30000;
  static const int receiveTimeout = 30000;

  // 存储键名
  static const String keyThemeMode = 'theme_mode';
  static const String keyLanguage = 'language';
  static const String keyFirstLaunch = 'first_launch';

  // 页面路由
  static const String routeHome = '/';
  static const String routeList = '/list';
  static const String routeDetail = '/detail';
  static const String routeGeneration = '/generation';
  static const String routeResult = '/result';
  static const String routeProfile = '/profile';
  static const String routeWebView = '/webview';

  // 协议链接
  static const String userAgreementUrl =
      'https://docs.qq.com/doc/DZkpPUXJkQU5SZ2NL';
  static const String privacyPolicyUrl =
      'https://docs.qq.com/doc/DZmRRS21yY1dObU9I';

  // 动画时长
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Duration shortAnimationDuration = Duration(milliseconds: 150);

  // 尺寸常量
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 8.0;
  static const double iconSize = 24.0;

  // AI头像生成配置
  static const int maxGenerationTime = 60; // 最大生成时间（秒）
  static const int freeGenerationLimit = 5; // 免费用户生成限制
  static const List<String> supportedImageFormats = ['jpg', 'jpeg', 'png'];

  // 会员配置
  static const int vipGenerationLimit = -1; // VIP用户无限制（-1表示无限）
  static const List<String> vipFeatures = [
    '解锁所有风格',
    '无限次生成',
    '高清下载',
    '优先处理',
    '专属客服',
  ];
}

import 'package:flutter/material.dart';

/// 应用颜色常量 - AI头像生成应用
class AppColors {
  // 主色调 - 粉红色系
  static const Color primary = Color(0xFFFF6B9D);
  static const Color primaryDark = Color(0xFFE91E63);
  static const Color primaryLight = Color(0xFFFFB3D1);

  // 辅助色 - 温暖色调
  static const Color secondary = Color(0xFFFF9800);
  static const Color secondaryDark = Color(0xFFF57C00);
  static const Color secondaryLight = Color(0xFFFFCC80);
  static const Color accent = Color(0xFFFFD700); // 金色强调色

  // 中性色
  static const Color background = Color(0xFFFAFAFA);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceDark = Color(0xFF1F2937);
  static const Color backgroundDark = Color(0xFF111827);

  // 文本色
  static const Color textPrimary = Color(0xFF1F2937);
  static const Color textSecondary = Color(0xFF6B7280);
  static const Color textDisabled = Color(0xFF9CA3AF);
  static const Color textPrimaryDark = Color(0xFFF9FAFB);
  static const Color textSecondaryDark = Color(0xFFD1D5DB);

  // 状态色
  static const Color success = Color(0xFF10B981);
  static const Color warning = Color(0xFFF59E0B);
  static const Color error = Color(0xFFEF4444);
  static const Color info = Color(0xFF3B82F6);

  // 边框色
  static const Color border = Color(0xFFE5E7EB);
  static const Color borderDark = Color(0xFF374151);

  // 阴影色
  static const Color shadow = Color(0x1A000000);
  static const Color shadowDark = Color(0x40000000);

  // 渐变色 - 粉红色渐变
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient backgroundGradient = LinearGradient(
    colors: [Color(0xFFFFF5F8), Color(0xFFFFE4EC)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  // AI头像生成专用颜色
  static const Color avatarBackground = Color(0xFFF8F9FA);
  static const Color styleCardBorder = Color(0xFFFFE4EC);
  static const Color membershipGold = Color(0xFFFFD700);
  static const Color membershipSilver = Color(0xFFC0C0C0);
  static const Color generatingProgress = Color(0xFF4CAF50);
}

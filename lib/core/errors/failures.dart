/// 失败结果基类
abstract class Failure {
  final String message;
  final String? code;

  const Failure(this.message, {this.code});

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Failure && other.message == message && other.code == code;
  }

  @override
  int get hashCode => message.hashCode ^ code.hashCode;

  @override
  String toString() => 'Failure(message: $message, code: $code)';
}

/// 网络失败
class NetworkFailure extends Failure {
  const NetworkFailure(super.message, {super.code});
}

/// 服务器失败
class ServerFailure extends Failure {
  const ServerFailure(super.message, {super.code});
}

/// 缓存失败
class CacheFailure extends Failure {
  const CacheFailure(super.message, {super.code});
}

/// 权限失败
class PermissionFailure extends Failure {
  const PermissionFailure(super.message, {super.code});
}

/// AI生成失败
class AIGenerationFailure extends Failure {
  const AIGenerationFailure(super.message, {super.code});
}

/// 图片处理失败
class ImageProcessingFailure extends Failure {
  const ImageProcessingFailure(super.message, {super.code});
}

/// 验证失败
class ValidationFailure extends Failure {
  const ValidationFailure(super.message, {super.code});
}

/// 未知失败
class UnknownFailure extends Failure {
  const UnknownFailure(super.message, {super.code});
}

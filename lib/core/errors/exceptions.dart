/// 自定义异常基类
abstract class AppException implements Exception {
  final String message;
  final String? code;
  final dynamic details;

  const AppException(this.message, {this.code, this.details});

  @override
  String toString() => 'AppException: $message';
}

/// 网络异常
class NetworkException extends AppException {
  const NetworkException(super.message, {super.code, super.details});
}

/// 服务器异常
class ServerException extends AppException {
  const ServerException(super.message, {super.code, super.details});
}

/// 缓存异常
class CacheException extends AppException {
  const CacheException(super.message, {super.code, super.details});
}

/// 权限异常
class PermissionException extends AppException {
  const PermissionException(super.message, {super.code, super.details});
}

/// 图片处理异常
class ImageProcessingException extends AppException {
  const ImageProcessingException(super.message, {super.code, super.details});
}

/// AI生成异常
class AIGenerationException extends AppException {
  const AIGenerationException(super.message, {super.code, super.details});
}

/// 验证异常
class ValidationException extends AppException {
  const ValidationException(super.message, {super.code, super.details});
}

/// 未知异常
class UnknownException extends AppException {
  const UnknownException(super.message, {super.code, super.details});
}

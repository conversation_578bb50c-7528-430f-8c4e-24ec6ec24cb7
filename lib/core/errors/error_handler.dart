import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'exceptions.dart';
import 'failures.dart';

/// 全局错误处理器
class ErrorHandler {
  static final Logger _logger = Logger();

  /// 处理异常并转换为失败
  static Failure handleException(Exception exception) {
    _logger.e('处理异常: $exception');

    if (exception is AppException) {
      return _handleAppException(exception);
    }

    // 未知异常
    return UnknownFailure(
      '发生未知错误: ${exception.toString()}',
      code: 'UNKNOWN_ERROR',
    );
  }

  /// 处理应用异常
  static Failure _handleAppException(AppException exception) {
    switch (exception.runtimeType) {
      case NetworkException _:
        return NetworkFailure(exception.message, code: exception.code);
      case ServerException _:
        return ServerFailure(exception.message, code: exception.code);
      case CacheException _:
        return CacheFailure(exception.message, code: exception.code);
      case PermissionException _:
        return PermissionFailure(exception.message, code: exception.code);
      case ImageProcessingException _:
        return ImageProcessingFailure(exception.message, code: exception.code);
      case AIGenerationException _:
        return AIGenerationFailure(exception.message, code: exception.code);
      case ValidationException _:
        return ValidationFailure(exception.message, code: exception.code);
      default:
        return UnknownFailure(exception.message, code: exception.code);
    }
  }

  /// 显示错误消息
  static void showError(Failure failure) {
    String title = '错误';
    String message = failure.message;

    // 根据错误类型设置不同的标题
    switch (failure.runtimeType) {
      case NetworkFailure _:
        title = '网络错误';
        break;
      case ServerFailure _:
        title = '服务器错误';
        break;
      case PermissionFailure _:
        title = '权限错误';
        break;
      case ImageProcessingFailure _:
        title = '图片处理错误';
        break;
      case AIGenerationFailure _:
        title = 'AI生成错误';
        break;
      case ValidationFailure _:
        title = '验证错误';
        break;
    }

    Get.snackbar(
      title,
      message,
      snackPosition: SnackPosition.TOP,
      duration: const Duration(seconds: 3),
    );
  }

  /// 记录错误日志
  static void logError(
    dynamic error, {
    StackTrace? stackTrace,
    String? context,
  }) {
    final errorMessage = context != null
        ? '$context: $error'
        : error.toString();

    _logger.e(errorMessage, error: error, stackTrace: stackTrace);

    // 在调试模式下打印到控制台
    if (kDebugMode) {
      developer.log(
        errorMessage,
        error: error,
        stackTrace: stackTrace,
        name: 'ErrorHandler',
      );
    }
  }

  /// 初始化全局错误处理
  static void initialize() {
    // Flutter框架错误处理
    FlutterError.onError = (FlutterErrorDetails details) {
      logError(
        details.exception,
        stackTrace: details.stack,
        context: 'Flutter Framework Error',
      );
    };

    // 异步错误处理
    PlatformDispatcher.instance.onError = (error, stack) {
      logError(error, stackTrace: stack, context: 'Async Error');
      return true;
    };
  }
}

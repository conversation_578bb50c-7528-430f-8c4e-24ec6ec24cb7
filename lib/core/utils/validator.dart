/// 验证工具类
class Validator {
  /// 验证邮箱
  static bool isEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// 验证手机号
  static bool isPhoneNumber(String phone) {
    return RegExp(r'^1[3-9]\d{9}$').hasMatch(phone);
  }

  /// 验证密码强度
  static bool isStrongPassword(String password) {
    // 至少8位，包含大小写字母、数字和特殊字符
    return RegExp(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$')
        .hasMatch(password);
  }

  /// 验证身份证号
  static bool isIdCard(String idCard) {
    return RegExp(r'^\d{17}[\dXx]$').hasMatch(idCard);
  }

  /// 验证URL
  static bool isUrl(String url) {
    return RegExp(r'^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$')
        .hasMatch(url);
  }

  /// 验证IP地址
  static bool isIpAddress(String ip) {
    return RegExp(r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$')
        .hasMatch(ip);
  }

  /// 验证中文字符
  static bool isChinese(String text) {
    return RegExp(r'^[\u4e00-\u9fa5]+$').hasMatch(text);
  }

  /// 验证数字
  static bool isNumber(String text) {
    return RegExp(r'^\d+$').hasMatch(text);
  }

  /// 验证小数
  static bool isDecimal(String text) {
    return RegExp(r'^\d+(\.\d+)?$').hasMatch(text);
  }

  /// 验证字符串长度
  static bool isLengthValid(String text, int minLength, int maxLength) {
    return text.length >= minLength && text.length <= maxLength;
  }

  /// 验证是否为空
  static bool isEmpty(String? text) {
    return text == null || text.trim().isEmpty;
  }

  /// 验证是否不为空
  static bool isNotEmpty(String? text) {
    return !isEmpty(text);
  }

  /// 获取邮箱验证错误信息
  static String? validateEmail(String? email) {
    if (isEmpty(email)) {
      return '请输入邮箱地址';
    }
    if (!isEmail(email!)) {
      return '请输入有效的邮箱地址';
    }
    return null;
  }

  /// 获取手机号验证错误信息
  static String? validatePhoneNumber(String? phone) {
    if (isEmpty(phone)) {
      return '请输入手机号';
    }
    if (!isPhoneNumber(phone!)) {
      return '请输入有效的手机号';
    }
    return null;
  }

  /// 获取密码验证错误信息
  static String? validatePassword(String? password) {
    if (isEmpty(password)) {
      return '请输入密码';
    }
    if (!isLengthValid(password!, 6, 20)) {
      return '密码长度应为6-20位';
    }
    return null;
  }

  /// 获取强密码验证错误信息
  static String? validateStrongPassword(String? password) {
    if (isEmpty(password)) {
      return '请输入密码';
    }
    if (!isStrongPassword(password!)) {
      return '密码至少8位，包含大小写字母、数字和特殊字符';
    }
    return null;
  }

  /// 获取必填字段验证错误信息
  static String? validateRequired(String? value, String fieldName) {
    if (isEmpty(value)) {
      return '请输入$fieldName';
    }
    return null;
  }
}

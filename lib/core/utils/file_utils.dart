import 'dart:io';
import 'dart:typed_data';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

/// 文件工具类
class FileUtils {
  /// 获取应用文档目录
  static Future<Directory> getDocumentsDirectory() async {
    return await getApplicationDocumentsDirectory();
  }

  /// 获取应用缓存目录
  static Future<Directory> getCacheDirectory() async {
    return await getTemporaryDirectory();
  }

  /// 获取外部存储目录
  static Future<Directory?> getExternalStorageDirectory() async {
    return await getExternalStorageDirectory();
  }

  /// 创建目录
  static Future<Directory> createDirectory(String dirPath) async {
    final directory = Directory(dirPath);
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }
    return directory;
  }

  /// 删除目录
  static Future<void> deleteDirectory(String dirPath) async {
    final directory = Directory(dirPath);
    if (await directory.exists()) {
      await directory.delete(recursive: true);
    }
  }

  /// 写入文件
  static Future<File> writeFile(String filePath, String content) async {
    final file = File(filePath);
    await file.writeAsString(content);
    return file;
  }

  /// 写入字节数据到文件
  static Future<File> writeBytes(String filePath, Uint8List bytes) async {
    final file = File(filePath);
    await file.writeAsBytes(bytes);
    return file;
  }

  /// 读取文件内容
  static Future<String> readFile(String filePath) async {
    final file = File(filePath);
    return await file.readAsString();
  }

  /// 读取文件字节数据
  static Future<Uint8List> readBytes(String filePath) async {
    final file = File(filePath);
    return await file.readAsBytes();
  }

  /// 复制文件
  static Future<File> copyFile(String sourcePath, String targetPath) async {
    final sourceFile = File(sourcePath);
    return await sourceFile.copy(targetPath);
  }

  /// 移动文件
  static Future<File> moveFile(String sourcePath, String targetPath) async {
    final sourceFile = File(sourcePath);
    return await sourceFile.rename(targetPath);
  }

  /// 删除文件
  static Future<void> deleteFile(String filePath) async {
    final file = File(filePath);
    if (await file.exists()) {
      await file.delete();
    }
  }

  /// 检查文件是否存在
  static Future<bool> fileExists(String filePath) async {
    final file = File(filePath);
    return await file.exists();
  }

  /// 获取文件大小
  static Future<int> getFileSize(String filePath) async {
    final file = File(filePath);
    return await file.length();
  }

  /// 获取文件扩展名
  static String getFileExtension(String filePath) {
    return path.extension(filePath);
  }

  /// 获取文件名（不含扩展名）
  static String getFileNameWithoutExtension(String filePath) {
    return path.basenameWithoutExtension(filePath);
  }

  /// 获取文件名（含扩展名）
  static String getFileName(String filePath) {
    return path.basename(filePath);
  }

  /// 获取文件目录路径
  static String getDirectoryPath(String filePath) {
    return path.dirname(filePath);
  }

  /// 格式化文件大小
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  /// 生成唯一文件名
  static String generateUniqueFileName(String extension) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '$timestamp$extension';
  }

  /// 获取图片保存路径
  static Future<String> getImageSavePath(String fileName) async {
    final directory = await getDocumentsDirectory();
    final imageDir = await createDirectory('${directory.path}/images');
    return '${imageDir.path}/$fileName';
  }

  /// 获取视频保存路径
  static Future<String> getVideoSavePath(String fileName) async {
    final directory = await getDocumentsDirectory();
    final videoDir = await createDirectory('${directory.path}/videos');
    return '${videoDir.path}/$fileName';
  }

  /// 获取音频保存路径
  static Future<String> getAudioSavePath(String fileName) async {
    final directory = await getDocumentsDirectory();
    final audioDir = await createDirectory('${directory.path}/audios');
    return '${audioDir.path}/$fileName';
  }

  /// 清理缓存目录
  static Future<void> clearCache() async {
    final cacheDir = await getCacheDirectory();
    if (await cacheDir.exists()) {
      await cacheDir.delete(recursive: true);
      await cacheDir.create();
    }
  }

  /// 获取目录大小
  static Future<int> getDirectorySize(String dirPath) async {
    final directory = Directory(dirPath);
    int size = 0;
    
    if (await directory.exists()) {
      await for (final entity in directory.list(recursive: true)) {
        if (entity is File) {
          size += await entity.length();
        }
      }
    }
    
    return size;
  }
}

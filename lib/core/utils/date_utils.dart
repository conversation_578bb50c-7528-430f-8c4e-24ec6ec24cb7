import 'package:intl/intl.dart';

/// 日期工具类
class AppDateUtils {
  /// 格式化日期时间
  static String formatDateTime(DateTime dateTime, {String? pattern}) {
    pattern ??= 'yyyy-MM-dd HH:mm:ss';
    return DateFormat(pattern).format(dateTime);
  }

  /// 格式化日期
  static String formatDate(DateTime dateTime, {String? pattern}) {
    pattern ??= 'yyyy-MM-dd';
    return DateFormat(pattern).format(dateTime);
  }

  /// 格式化时间
  static String formatTime(DateTime dateTime, {String? pattern}) {
    pattern ??= 'HH:mm:ss';
    return DateFormat(pattern).format(dateTime);
  }

  /// 获取相对时间描述
  static String getRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }

  /// 判断是否为今天
  static bool isToday(DateTime dateTime) {
    final now = DateTime.now();
    return dateTime.year == now.year &&
        dateTime.month == now.month &&
        dateTime.day == now.day;
  }

  /// 判断是否为昨天
  static bool isYesterday(DateTime dateTime) {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return dateTime.year == yesterday.year &&
        dateTime.month == yesterday.month &&
        dateTime.day == yesterday.day;
  }

  /// 获取月份开始时间
  static DateTime getMonthStart(DateTime dateTime) {
    return DateTime(dateTime.year, dateTime.month, 1);
  }

  /// 获取月份结束时间
  static DateTime getMonthEnd(DateTime dateTime) {
    return DateTime(dateTime.year, dateTime.month + 1, 0, 23, 59, 59);
  }

  /// 获取周开始时间（周一）
  static DateTime getWeekStart(DateTime dateTime) {
    final weekday = dateTime.weekday;
    return dateTime.subtract(Duration(days: weekday - 1));
  }

  /// 获取周结束时间（周日）
  static DateTime getWeekEnd(DateTime dateTime) {
    final weekday = dateTime.weekday;
    return dateTime.add(Duration(days: 7 - weekday, hours: 23, minutes: 59, seconds: 59));
  }
}

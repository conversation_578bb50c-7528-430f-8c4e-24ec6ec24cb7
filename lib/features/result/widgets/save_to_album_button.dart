import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 保存到相册按钮组件
class SaveToAlbumButton extends StatelessWidget {
  final VoidCallback onSavePressed;
  final VoidCallback onSharePressed;
  final bool isSaving;

  const SaveToAlbumButton({
    super.key,
    required this.onSavePressed,
    required this.onSharePressed,
    this.isSaving = false,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 640.w,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 分享图标按钮
          _buildShareIconButton(),

          // 保存到相册按钮
          _buildSaveButton(),
        ],
      ),
    );
  }

  /// 构建分享图标按钮
  Widget _buildShareIconButton() {
    return GestureDetector(
      onTap: onSharePressed,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(50.r),
          boxShadow: [
            BoxShadow(
              color: const Color.fromRGBO(164, 164, 164, 0.5),
              blurRadius: 14.r,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        padding: EdgeInsets.all(12.w),
        child: Container(
          decoration: BoxDecoration(
            color: const Color(0xFFFF6B6B),
            borderRadius: BorderRadius.circular(50.r),
          ),
          padding: EdgeInsets.fromLTRB(24.w, 23.h, 25.w, 23.h),
          child: Icon(Icons.share, size: 43.w, color: Colors.white),
        ),
      ),
    );
  }

  /// 构建保存按钮
  Widget _buildSaveButton() {
    return GestureDetector(
      onTap: isSaving ? null : onSavePressed,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(63.r),
          boxShadow: [
            BoxShadow(
              color: const Color.fromRGBO(255, 198, 198, 1),
              blurRadius: 14.r,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        padding: EdgeInsets.fromLTRB(14.w, 12.h, 13.w, 12.h),
        child: Container(
          decoration: BoxDecoration(
            color: isSaving ? const Color(0xFFCCCCCC) : const Color(0xFFFF6B6B),
            borderRadius: BorderRadius.circular(46.r),
          ),
          padding: EdgeInsets.fromLTRB(118.w, 18.h, 119.w, 17.h),
          child: Text(
            '保存至相册',
            style: TextStyle(
              color: Colors.white,
              fontSize: 41.sp,
              fontWeight: FontWeight.w500,
              letterSpacing: 0.95.w,
              height: 57.h / 41.sp,
            ),
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../shared/widgets/optimized_image.dart';

/// 结果图片展示组件
class ResultImageDisplay extends StatelessWidget {
  final String imageUrl;
  final bool isLoading;

  const ResultImageDisplay({
    super.key,
    required this.imageUrl,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 645.w,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(32.r),
        boxShadow: [
          BoxShadow(
            color: const Color.fromRGBO(164, 164, 164, 0.5),
            blurRadius: 14.r,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: EdgeInsets.all(40.w),
      child: Container(
        width: 571.w,
        height: 758.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(color: const Color(0xFF979797), width: 1.w),
          image: isLoading
              ? null
              : null, // 移除DecorationImage，改用OptimizedNetworkImage
        ),
        child: isLoading
            ? _buildLoadingWidget()
            : OptimizedNetworkImage(
                imageUrl: imageUrl,
                width: 571.w,
                height: 758.w,
                fit: BoxFit.contain,
                borderRadius: BorderRadius.circular(16.r),
              ),
      ),
    );
  }

  /// 构建加载中的组件
  Widget _buildLoadingWidget() {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFFF6B6B)),
        ),
      ),
    );
  }
}

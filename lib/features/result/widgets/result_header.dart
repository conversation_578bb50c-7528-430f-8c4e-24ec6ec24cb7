import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 结果页面头部组件
class ResultHeader extends StatelessWidget {
  final String title;
  final VoidCallback onBackPressed;

  const ResultHeader({
    super.key,
    required this.title,
    required this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(32.w, 31.h, 79.w, 0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 返回按钮
          GestureDetector(
            onTap: onBackPressed,
            child: Image.network(
              'https://lanhu-oss-2537-2.lanhuapp.com/SketchPngfc2819f3106015bb4daf9d2bb6c70043b43932c9adf08e6ba6b1fb041c81708f',
              width: 24.w,
              height: 44.w,
            ),
          ),

          // 标题
          Text(
            title,
            style: TextStyle(
              color: const Color(0xFF333333),
              fontSize: 36.sp,
              fontWeight: FontWeight.w500,
              letterSpacing: 1.29.w,
              height: 50.h / 36.sp,
            ),
          ),

          // 占位，保持标题居中
          SizedBox(width: 24.w),
        ],
      ),
    );
  }
}

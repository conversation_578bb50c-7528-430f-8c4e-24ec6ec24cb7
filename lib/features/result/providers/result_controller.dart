import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../../../core/services/image_save_service.dart';
import '../../../core/services/image_share_service.dart';
import '../../../core/services/permission_service.dart';

/// 生成结果页面控制器
class ResultController extends GetxController {
  final Logger _logger = Logger();
  // 生成的图片URL
  final _generatedImageUrl = ''.obs;
  String get generatedImageUrl => _generatedImageUrl.value;

  // 风格名称
  String _styleName = '';
  String get styleName => _styleName;

  // 原始图片
  File? _originalImage;
  File? get originalImage => _originalImage;

  // 加载状态
  final _isLoading = false.obs;
  bool get isLoading => _isLoading.value;

  // 保存状态
  final _isSaving = false.obs;
  bool get isSaving => _isSaving.value;

  @override
  void onInit() {
    super.onInit();
    _logger.d('ResultController onInit called');

    // 获取传入的参数
    final arguments = Get.arguments;
    _logger.d('Arguments received: $arguments');

    if (arguments != null && arguments is Map<String, dynamic>) {
      // 接收生成结果URL
      if (arguments['resultUrl'] != null) {
        _generatedImageUrl.value = arguments['resultUrl'] as String;
        _logger.d('Generated image URL: ${_generatedImageUrl.value}');
      }

      // 接收风格名称
      if (arguments['style'] != null) {
        _styleName = arguments['style'] as String;
        _logger.d('Style name: $_styleName');
      }

      // 接收原始图片
      if (arguments['originalImage'] != null) {
        _originalImage = arguments['originalImage'] as File;
        _logger.d('Original image path: ${_originalImage?.path}');
      }
    }
  }

  /// 保存到相册
  void saveToAlbum() async {
    _logger.d('Saving image to album: ${_generatedImageUrl.value}');

    _isSaving.value = true;
    update();

    try {
      // 检查图片URL是否有效
      if (_generatedImageUrl.value.isEmpty) {
        Get.snackbar('错误', '没有可保存的图片');
        return;
      }

      // 先检查相册权限
      _logger.d('检查相册权限...');
      final hasPermission = await PermissionService.requestPhotosPermission();
      if (!hasPermission) {
        _logger.w('相册权限被拒绝');
        Get.snackbar('权限不足', '需要相册权限才能保存图片');
        return;
      }

      _logger.d('相册权限检查通过，开始保存图片');
      // 使用ImageSaveService保存图片
      final success = await ImageSaveService.saveNetworkImageToGallery(
        _generatedImageUrl.value,
        showLoading: false, // 我们自己管理加载状态
        showSuccess: true,
        showError: true,
      );

      if (success) {
        _logger.d('Image saved successfully');
      } else {
        _logger.w('Image save failed');
      }
    } catch (e) {
      _logger.e('Error saving image: $e');
      Get.snackbar('保存失败', '保存图片时发生错误: $e');
    } finally {
      _isSaving.value = false;
      update();
    }
  }

  /// 分享图片
  void shareImage() async {
    _logger.d('Sharing image: ${_generatedImageUrl.value}');

    try {
      await ImageShareService.shareNetworkImage(
        _generatedImageUrl.value,
        text: '看看我用卡通相机制作的精彩作品！',
        subject: '卡通相机 - AI照片风格重绘',
      );
    } catch (e) {
      _logger.e('分享失败: $e');
    }
  }

  /// 重新生成
  void regenerate() {
    _logger.d('Regenerating image with style: $_styleName');

    if (_originalImage != null) {
      // 返回到详情页面重新生成
      Get.offNamed(
        '/detail',
        arguments: {'styleName': _styleName, 'originalImage': _originalImage},
      );
    } else {
      Get.snackbar('错误', '原始图片不可用，无法重新生成');
    }
  }

  /// 返回首页
  void goHome() {
    _logger.d('Going back to home');

    // 返回到首页，清除所有中间页面
    Get.offAllNamed('/');
  }

  /// 查看原图
  void viewOriginalImage() {
    _logger.d('Viewing original image: ${_originalImage?.path}');

    if (_originalImage != null && _originalImage!.existsSync()) {
      // 显示原图预览对话框
      Get.dialog(
        Dialog(
          child: Container(
            constraints: const BoxConstraints(maxHeight: 600),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                AppBar(
                  title: const Text('原图'),
                  automaticallyImplyLeading: false,
                  actions: [
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Get.back(),
                    ),
                  ],
                ),
                Expanded(
                  child: Image.file(_originalImage!, fit: BoxFit.contain),
                ),
              ],
            ),
          ),
        ),
      );
    } else {
      Get.snackbar('提示', '原图不可用');
    }
  }
}

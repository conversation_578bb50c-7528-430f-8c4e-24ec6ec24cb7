import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../providers/result_controller.dart';
import '../widgets/result_header.dart';
import '../widgets/result_image_display.dart';
import '../widgets/save_to_album_button.dart';

/// 生成结果页面
class ResultPage extends StatelessWidget {
  const ResultPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ResultController>(
      builder: (controller) {
        return Scaffold(
          backgroundColor: Colors.white,
          body: Safe<PERSON><PERSON>(
            child: Column(
              children: [
                // 导航栏
                ResultHeader(title: '生成结束', onBackPressed: () => Get.back()),

                SizedBox(height: 134.h),

                // 结果图片展示区域
                ResultImageDisplay(
                  imageUrl: controller.generatedImageUrl,
                  isLoading: controller.isLoading,
                ),

                SizedBox(height: 79.h),

                // 底部操作区域
                SaveToAlbumButton(
                  onSavePressed: controller.saveToAlbum,
                  onSharePressed: controller.shareImage,
                  isSaving: controller.isSaving,
                ),

                // SizedBox(height: 290.h), // 底部间距
              ],
            ),
          ),
        );
      },
    );
  }
}

import 'package:cartoon_camera/shared/widgets/back_icon.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../providers/list_controller.dart';
import '../widgets/style_filter_tabs.dart';
import '../widgets/style_grid_item.dart';

/// 风格列表页面
class ListPage extends StatelessWidget {
  const ListPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ListController>(
      init: ListController(),
      builder: (controller) {
        return Scaffold(
          backgroundColor: Colors.white,
          body: SafeArea(
            child: Padding(
              padding: EdgeInsets.fromLTRB(32.w, 31.h, 32.w, 0),
              child: Column(
                children: [
                  // 顶部导航栏
                  _buildHeader(),

                  Sized<PERSON>ox(height: 31.h),

                  // 筛选标签
                  StyleFilterTabs(
                    selectedIndex: controller.selectedFilterIndex,
                    onTabSelected: controller.selectFilter,
                    tabs: controller.filterTabs,
                  ),

                  SizedBox(height: 13.h),

                  // 风格网格
                  Expanded(child: _buildStyleGrid(controller)),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建顶部导航栏
  Widget _buildHeader() {
    return Row(
      children: [
        // 返回按钮
        BackIcon(),

        Expanded(
          child: Text(
            '风格列表',
            style: TextStyle(
              color: const Color(0xFF333333),
              fontSize: 36.sp,
              fontWeight: FontWeight.w500,
              letterSpacing: 1.29.w,
              height: 50.h / 36.sp,
            ),
            textAlign: TextAlign.center,
          ),
        ),

        // 占位，保持标题居中
        SizedBox(width: 24.w),
      ],
    );
  }

  /// 构建风格网格
  Widget _buildStyleGrid(ListController controller) {
    return Obx(() {
      if (controller.isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      final filteredStyles = controller.getFilteredStyles();

      return GridView.builder(
        padding: EdgeInsets.zero,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 331.w / 439.w,
          crossAxisSpacing: 24.w,
          mainAxisSpacing: 24.w,
        ),
        itemCount: filteredStyles.length,
        itemBuilder: (context, index) {
          final style = filteredStyles[index];
          return StyleGridItem(
            style: style,
            onTap: () => controller.navigateToStyleDetail(style.style),
          );
        },
      );
    });
  }
}

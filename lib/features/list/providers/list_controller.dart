import 'package:get/get.dart';
import '../../../shared/models/template_response_model.dart';
import '../../../controllers/app_controller.dart';

/// 风格列表页面控制器
class ListController extends GetxController {
  late final AppController _appController;

  // 当前选中的筛选索引
  final _selectedFilterIndex = 1.obs; // 默认选中"经典动漫"
  int get selectedFilterIndex => _selectedFilterIndex.value;

  // 筛选标签列表（只保留经典动漫和手绘插画）
  final filterTabs = ['全部', '经典动漫', '手绘插画'];

  // 直接从 AppController 获取加载状态
  bool get isLoading =>
      _appController.isLoadingData.value ||
      _appController.templateData.value == null;

  // 直接从 AppController 获取模板数据
  List<TemplateStyleItem> get animeTemplates =>
      _appController.templateData.value?.anime ?? [];
  List<TemplateStyleItem> get illustrationTemplates =>
      _appController.templateData.value?.illustrations ?? [];

  @override
  void onInit() {
    super.onInit();
    // 获取 AppController 实例
    _appController = Get.find<AppController>();

    // 检查是否有传入的分类参数
    final arguments = Get.arguments;
    if (arguments != null && arguments['category'] != null) {
      final category = arguments['category'] as String;
      final index = filterTabs.indexOf(category);
      if (index != -1) {
        _selectedFilterIndex.value = index;
      }
    }
  }

  /// 选择筛选标签
  void selectFilter(int index) {
    _selectedFilterIndex.value = index;
    update();
  }

  /// 获取筛选后的风格列表
  List<TemplateStyleItem> getFilteredStyles() {
    if (selectedFilterIndex == 0) {
      // 全部：合并动漫和插画模板
      return [...animeTemplates, ...illustrationTemplates];
    } else if (selectedFilterIndex == 1) {
      // 经典动漫
      return animeTemplates;
    } else if (selectedFilterIndex == 2) {
      // 手绘插画
      return illustrationTemplates;
    }
    return [];
  }

  /// 跳转到风格详情页
  void navigateToStyleDetail(String styleName) {
    // 查找对应的风格数据
    final allTemplates = [...animeTemplates, ...illustrationTemplates];
    final template = allTemplates.firstWhere(
      (t) => t.style == styleName,
      orElse: () => TemplateStyleItem(style: styleName, template: ''),
    );

    // 直接传递 TemplateStyleItem 对象，与 HomeController 保持一致
    Get.toNamed('/detail', arguments: template);
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../shared/models/template_response_model.dart';

/// 风格网格项组件
class StyleGridItem extends StatelessWidget {
  final TemplateStyleItem style;
  final VoidCallback onTap;

  const StyleGridItem({super.key, required this.style, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 331.w,
        height: 439.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(18.r),
          border: Border.all(color: const Color(0xFF979797), width: 1.w),
          image: DecorationImage(
            image: NetworkImage(
              style.template.isNotEmpty
                  ? style.template
                  : 'https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/475d61d0222a4ad08dfd2623deec2d59_mergeImage.png',
            ),
            fit: BoxFit.cover,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Container(
              width: double.infinity,
              height: 84.w,
              // margin: EdgeInsets.only(bottom: 16.w),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  stops: [0.0, 1.0],
                  colors: [
                    Color.fromRGBO(0, 0, 0, 0), // rgba(0,0,0,0)
                    Color.fromRGBO(0, 0, 0, 0.8), // rgba(0,0,0,0.8)
                  ],
                ),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(18.r),
                  bottomRight: Radius.circular(18.r),
                ),
              ),
              child: Center(
                child: Text(
                  style.style,
                  softWrap: false,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 36.w,
                    fontWeight: FontWeight.normal,
                    height: 50.w / 36.w,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 风格筛选标签组件
class StyleFilterTabs extends StatelessWidget {
  final int selectedIndex;
  final Function(int) onTabSelected;
  final List<String>? tabs; // 可选的自定义标签列表

  const StyleFilterTabs({
    super.key,
    required this.selectedIndex,
    required this.onTabSelected,
    this.tabs,
  });

  @override
  Widget build(BuildContext context) {
    final tabList = tabs ?? ['全部', '经典动漫', '手绘插画'];

    return SizedBox(
      height: 66.h, // 固定高度，避免布局问题
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 0), // 移除默认padding
        child: Row(
          children: tabList.asMap().entries.map((entry) {
            final index = entry.key;
            final title = entry.value;
            final isSelected = index == selectedIndex;

            return Container(
              margin: EdgeInsets.only(
                right: index < tabList.length - 1 ? 24.w : 0, // 标签间距
              ),
              child: GestureDetector(
                onTap: () => onTabSelected(index),
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 41.w,
                    vertical: isSelected ? 13.h : 12.h,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? const Color(0xFFFF6B6B)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(33.r),
                    border: isSelected
                        ? null
                        : Border.all(
                            color: const Color(0xFFFF6B6B),
                            width: 1.w,
                          ),
                  ),
                  child: Text(
                    title,
                    style: TextStyle(
                      color: isSelected
                          ? Colors.white
                          : const Color(0xFFFF6B6B),
                      fontSize: 28.sp,
                      fontWeight: FontWeight.normal,
                      height: 40.h / 28.sp,
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}

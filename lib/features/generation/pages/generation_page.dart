import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../providers/generation_controller.dart';
import '../../../shared/widgets/optimized_image.dart';

/// 图片生成页面 - 根据HTML/CSS设计稿实现
class GenerationPage extends StatelessWidget {
  const GenerationPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<GenerationController>();
    return Obx(() {
      final progressPercent = (controller.generationProgress * 100).toInt();
      // 调试信息
      print('Generation页面进度: $progressPercent%');
      return Scaffold(
        backgroundColor: const Color(0xFFFFFFFF),
        body: SafeArea(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 30.w),
            child: Column(
              children: [
                // 导航栏
                _buildNavigationBar(),

                // 主要内容区域
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 24.w), // 减少左右边距
                    child: Column(
                      children: [
                        SizedBox(height: 94.h),

                        // AI魔法师文案区域
                        SizedBox(
                          width: double.infinity,
                          child: RichText(
                            textAlign: TextAlign.left,
                            text: TextSpan(
                              style: TextStyle(
                                fontSize: 38.w,
                                fontWeight: FontWeight.w500,
                                color: const Color(0xFF333333),
                                height: 62.w / 38.w,
                                letterSpacing: 3.14, // 添加字间距，匹配设计稿
                              ),
                              children: [
                                const TextSpan(text: 'AI魔法师\n'),
                                TextSpan(text: '正在挥动风格魔杖中···$progressPercent%'),
                              ],
                            ),
                          ),
                        ),
                        SizedBox(height: 29.h),

                        // 提示文本
                        SizedBox(
                          width: double.infinity,
                          child: Text(
                            '别着急，等魔法光芒散去，惊喜就会出现啦！',
                            style: TextStyle(
                              fontSize: 30.w, // 增大字号，匹配设计稿
                              fontWeight: FontWeight.w500,
                              color: const Color(0xFF999999),
                              letterSpacing: 1.07, // 调整字间距
                              height: 42.w / 30.w, // 调整行高
                            ),
                          ),
                        ),

                        SizedBox(height: 60.h),

                        // 中间的卡通图片
                        Expanded(
                          child: SizedBox(
                            width: double.infinity,
                            child: OptimizedNetworkImage(
                              imageUrl:
                                  'https://lanhu-oss-proxy.lanhuapp.com/SketchPng3d91a3ab82b5631b702d0006aaf2a0a3e11968d07f9a5f10e5a002efb41bce48',
                              fit: BoxFit.contain,
                            ),
                          ),
                        ),

                        SizedBox(height: 40.h),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }

  /// 构建导航栏
  Widget _buildNavigationBar() {
    return Container(
      height: 88.h,
      padding: EdgeInsets.symmetric(horizontal: 32.w),
      child: Row(
        children: [
          // 返回按钮
          GestureDetector(
            onTap: () => Get.back(),
            child: SizedBox(
              width: 24.w,
              height: 44.h,
              child: Image.network(
                'https://lanhu-oss-proxy.lanhuapp.com/SketchPngfc2819f3106015bb4daf9d2bb6c70043b43932c9adf08e6ba6b1fb041c81708f',
                fit: BoxFit.contain,
              ),
            ),
          ),

          // 标题
          Expanded(
            child: Text(
              '图像生成',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 36.w,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF333333),
              ),
            ),
          ),

          // 占位，保持标题居中
          SizedBox(width: 24.w),
        ],
      ),
    );
  }
}

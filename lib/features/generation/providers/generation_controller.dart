import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:logger/logger.dart';
import '../../../core/services/permission_service.dart';
import '../../../core/services/oss_service.dart';
import '../../../services/api_service.dart';
import '../../../shared/models/template_response_model.dart';

/// 图片生成结果
class GenerationResult {
  final bool success;
  final String? resultUrl;
  final String? errorMessage;

  GenerationResult({required this.success, this.resultUrl, this.errorMessage});
}

/// 生成页面控制器
class GenerationController extends GetxController {
  final Logger _logger = Logger();
  late final ApiService _apiService;
  late final OssService _ossService;

  final ImagePicker _imagePicker = ImagePicker();

  // 选中的图片
  final _selectedImage = Rx<File?>(null);
  File? get selectedImage => _selectedImage.value;

  // 选中的风格
  final _selectedStyle = ''.obs;
  String get selectedStyle => _selectedStyle.value;
  set selectedStyle(String value) => _selectedStyle.value = value;

  // 选中的尺寸
  final _selectedSize = '2:3'.obs;
  String get selectedSize => _selectedSize.value;
  set selectedSize(String value) => _selectedSize.value = value;

  // 生成状态
  final _isGenerating = false.obs;
  bool get isGenerating => _isGenerating.value;

  // 生成进度
  final _generationProgress = 0.0.obs;
  double get generationProgress => _generationProgress.value;

  @override
  void onInit() {
    super.onInit();

    // 初始化服务
    _apiService = Get.find<ApiService>();
    _ossService = Get.find<OssService>();

    // 从路由参数获取数据
    final arguments = Get.arguments;
    if (arguments != null && arguments is Map<String, dynamic>) {
      // 接收从 DetailController 传递的参数
      if (arguments['imageFile'] != null) {
        _selectedImage.value = arguments['imageFile'] as File;
      }
      if (arguments['aspectRatio'] != null) {
        _selectedSize.value = arguments['aspectRatio'] as String;
      }
      if (arguments['styleName'] != null) {
        _selectedStyle.value = arguments['styleName'] as String;
      }

      // 如果有图片，自动开始生成
      if (_selectedImage.value != null) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          startGeneration();
        });
      }
    } else {
      // 如果没有参数，启动测试进度
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _startTestProgress();
      });
    }
  }

  /// 测试进度更新（用于调试）
  void _startTestProgress() {
    _logger.d('开始测试进度更新');
    _isGenerating.value = true;
    _generationProgress.value = 0.1;
    _logger.d('初始进度设置为: ${(_generationProgress.value * 100).toInt()}%');
    _startFakeProgressUpdate();
  }

  /// 从相机拍照
  Future<void> pickImageFromCamera() async {
    try {
      // 检查相机权限
      final hasPermission = await PermissionService.requestCameraPermission();
      if (!hasPermission) {
        PermissionService.showPermissionDeniedMessage('相机');
        return;
      }

      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (image != null) {
        _selectedImage.value = File(image.path);
      }
    } catch (e) {
      Get.snackbar('错误', '拍照失败: $e');
    }
  }

  /// 从相册选择
  Future<void> pickImageFromGallery() async {
    try {
      // 检查相册权限
      final hasPermission = await PermissionService.requestPhotosPermission();
      if (!hasPermission) {
        PermissionService.showPermissionDeniedMessage('相册');
        return;
      }

      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (image != null) {
        _selectedImage.value = File(image.path);
      }
    } catch (e) {
      Get.snackbar('错误', '选择图片失败: $e');
    }
  }

  /// 显示图片选择对话框
  void showImagePickerDialog() {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(20),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              '选择图片',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildPickerOption(
                  icon: Icons.camera_alt,
                  label: '拍照',
                  onTap: () {
                    Get.back();
                    pickImageFromCamera();
                  },
                ),
                _buildPickerOption(
                  icon: Icons.photo_library,
                  label: '相册',
                  onTap: () {
                    Get.back();
                    pickImageFromGallery();
                  },
                ),
              ],
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildPickerOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(30),
            ),
            child: Icon(icon, size: 30, color: Colors.blue),
          ),
          const SizedBox(height: 8),
          Text(label, style: const TextStyle(fontSize: 14)),
        ],
      ),
    );
  }

  /// 生成卡通图片
  Future<GenerationResult> _generateCartoon({
    required File imageFile,
    required String style,
    required String size,
  }) async {
    try {
      // 1. 上传图片到OSS
      _logger.d('开始上传图片到OSS');
      final imageUrl = await _ossService.uploadImage(imageFile);
      _logger.d('OSS上传结果: $imageUrl');

      if (imageUrl == null) {
        _logger.e('OSS上传失败，返回null');
        return GenerationResult(success: false, errorMessage: '图片上传失败');
      }

      _logger.d('OSS上传成功，CDN地址: $imageUrl');

      // 2. 调用生成API
      _logger.d('开始调用生成API');
      final request = ImageGenerationRequest(
        url: imageUrl,
        style: style,
        size: size,
      );

      _logger.d('生成请求参数: url=$imageUrl, style=$style, size=$size');

      final resultUrl = await _apiService.generateCartoon(request);
      _logger.d('生成API返回结果: $resultUrl');

      return GenerationResult(success: true, resultUrl: resultUrl);
    } catch (e) {
      _logger.e('生成图片失败: $e');
      return GenerationResult(success: false, errorMessage: e.toString());
    }
  }

  /// 验证图片格式
  bool _validateImageFormat(File imageFile) {
    final extension = imageFile.path.split('.').last.toLowerCase();
    const supportedFormats = ['jpg', 'jpeg', 'png'];
    return supportedFormats.contains(extension);
  }

  /// 验证图片大小
  bool _validateImageSize(File imageFile) {
    try {
      final fileSize = imageFile.lengthSync();
      const maxSize = 10 * 1024 * 1024; // 10MB
      return fileSize <= maxSize;
    } catch (e) {
      _logger.e('验证图片大小失败: $e');
      return false;
    }
  }

  /// 开始生成
  Future<void> startGeneration() async {
    if (_selectedImage.value == null) {
      Get.snackbar('提示', '请先选择图片');
      return;
    }

    if (_selectedStyle.value.isEmpty) {
      Get.snackbar('提示', '请选择风格');
      return;
    }

    // 验证图片
    if (!_validateImageFormat(_selectedImage.value!)) {
      Get.snackbar('提示', '不支持的图片格式，请选择 JPG 或 PNG 格式的图片');
      return;
    }

    if (!_validateImageSize(_selectedImage.value!)) {
      Get.snackbar('提示', '图片大小不能超过 10MB');
      return;
    }

    _isGenerating.value = true;
    _generationProgress.value = 0.1; // 从10%开始

    try {
      // 启动假进度更新
      _startFakeProgressUpdate();

      // 开始生成
      final result = await _generateCartoon(
        imageFile: _selectedImage.value!,
        style: _selectedStyle.value,
        size: _selectedSize.value,
      );

      // 生成完成，立即跳到100%
      _updateProgress(1.0);

      if (result.success) {
        // 等待一下让用户看到100%
        await Future.delayed(const Duration(milliseconds: 500));

        // 跳转到结果页面，使用offNamed替换当前页面，避免返回时回到generation页面
        Get.offNamed(
          '/result',
          arguments: {
            'resultUrl': result.resultUrl,
            'originalImage': _selectedImage.value,
            'style': _selectedStyle.value,
          },
        );
      } else {
        Get.snackbar('生成失败', result.errorMessage ?? '未知错误');
      }
    } catch (e) {
      Get.snackbar('错误', '生成过程中发生错误: $e');
    } finally {
      _isGenerating.value = false;
      _generationProgress.value = 0.0;
    }
  }

  /// 启动假进度更新 - 从10%逐渐增加到90%
  void _startFakeProgressUpdate() {
    Timer.periodic(const Duration(milliseconds: 800), (timer) {
      if (!_isGenerating.value || _generationProgress.value >= 0.9) {
        timer.cancel();
        return;
      }

      // 每次增加3-8%的随机进度
      final random = DateTime.now().millisecondsSinceEpoch % 100;
      final increment = 0.03 + (0.05 * random / 100);
      final newProgress = (_generationProgress.value + increment).clamp(
        0.1,
        0.9,
      );

      _logger.d(
        '进度更新: ${(_generationProgress.value * 100).toInt()}% -> ${(newProgress * 100).toInt()}%',
      );
      _updateProgress(newProgress);
    });
  }

  /// 更新进度
  void _updateProgress(double progress) {
    _generationProgress.value = progress;
    update(); // 触发UI更新
  }

  /// 重新选择图片
  void reselectImage() {
    _selectedImage.value = null;
    showImagePickerDialog();
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 生成页面头部组件
class GenerationHeader extends StatelessWidget {
  final String title;
  final VoidCallback onBackPressed;

  const GenerationHeader({
    super.key,
    required this.title,
    required this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 88.h,
      padding: EdgeInsets.symmetric(horizontal: 32.w),
      child: Row(
        children: [
          // 返回按钮
          GestureDetector(
            onTap: onBackPressed,
            child: Container(
              width: 44.w,
              height: 44.w,
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(22.w),
              ),
              child: Icon(
                Icons.arrow_back_ios_new,
                size: 20.w,
                color: Colors.black87,
              ),
            ),
          ),

          // 标题
          Expanded(
            child: Text(
              title,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 36.sp,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
          ),

          // 占位，保持标题居中
          SizedBox(width: 44.w),
        ],
      ),
    );
  }
}

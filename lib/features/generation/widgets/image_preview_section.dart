import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 图片预览区域组件
class ImagePreviewSection extends StatelessWidget {
  final File? selectedImage;
  final VoidCallback onImageTap;

  const ImagePreviewSection({
    super.key,
    required this.selectedImage,
    required this.onImageTap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 标题
        Text(
          '选择照片',
          style: TextStyle(
            fontSize: 32.sp,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),

        SizedBox(height: 24.h),

        // 图片预览区域
        GestureDetector(
          onTap: onImageTap,
          child: Container(
            width: 300.w,
            height: 300.w,
            decoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20.w),
              border: Border.all(
                color: Colors.grey.withValues(alpha: 0.3),
                width: 2.w,
              ),
            ),
            child: selectedImage != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(18.w),
                    child: Image.file(selectedImage!, fit: BoxFit.cover),
                  )
                : Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.add_photo_alternate_outlined,
                        size: 80.w,
                        color: Colors.grey.withValues(alpha: 0.6),
                      ),
                      SizedBox(height: 16.h),
                      Text(
                        '点击选择照片',
                        style: TextStyle(
                          fontSize: 28.sp,
                          color: Colors.grey.withValues(alpha: 0.8),
                        ),
                      ),
                    ],
                  ),
          ),
        ),

        if (selectedImage != null) ...[
          SizedBox(height: 16.h),
          GestureDetector(
            onTap: onImageTap,
            child: Text(
              '重新选择',
              style: TextStyle(
                fontSize: 24.sp,
                color: Colors.blue,
                decoration: TextDecoration.underline,
              ),
            ),
          ),
        ],
      ],
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 风格选择区域组件
class StyleSelectionSection extends StatelessWidget {
  final String selectedStyle;
  final Function(String) onStyleChanged;

  const StyleSelectionSection({
    super.key,
    required this.selectedStyle,
    required this.onStyleChanged,
  });

  // 可选风格列表
  static const List<String> availableStyles = [
    '宫崎骏',
    '吉卜力',
    '二次元',
    '经典日漫',
    '经典港漫',
    '韩漫',
    '卡通简笔画',
    '卡通插画',
    '彩铅手绘',
    '素描',
    '水彩',
    '工笔画',
  ];

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        Text(
          '选择风格',
          style: TextStyle(
            fontSize: 32.sp,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),

        SizedBox(height: 24.h),

        // 风格网格
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 16.w,
            mainAxisSpacing: 16.h,
            childAspectRatio: 2.5,
          ),
          itemCount: availableStyles.length,
          itemBuilder: (context, index) {
            final style = availableStyles[index];
            final isSelected = style == selectedStyle;

            return GestureDetector(
              onTap: () => onStyleChanged(style),
              child: Container(
                decoration: BoxDecoration(
                  color: isSelected
                      ? Colors.blue
                      : Colors.grey.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12.w),
                  border: Border.all(
                    color: isSelected
                        ? Colors.blue
                        : Colors.grey.withValues(alpha: 0.3),
                    width: 2.w,
                  ),
                ),
                child: Center(
                  child: Text(
                    style,
                    style: TextStyle(
                      fontSize: 24.sp,
                      fontWeight: FontWeight.w500,
                      color: isSelected ? Colors.white : Colors.black87,
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 生成按钮组件
class GenerationButton extends StatelessWidget {
  final bool isGenerating;
  final double progress;
  final VoidCallback onPressed;

  const GenerationButton({
    super.key,
    required this.isGenerating,
    required this.progress,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 88.h,
      margin: EdgeInsets.symmetric(horizontal: 32.w),
      child: ElevatedButton(
        onPressed: isGenerating ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: isGenerating ? Colors.grey : Colors.blue,
          foregroundColor: Colors.white,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(44.h),
          ),
        ),
        child: isGenerating
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 24.w,
                    height: 24.w,
                    child: CircularProgressIndicator(
                      strokeWidth: 2.w,
                      valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                      value: progress > 0 ? progress : null,
                    ),
                  ),
                  SizedBox(width: 16.w),
                  Text(
                    progress > 0 
                        ? '生成中... ${(progress * 100).toInt()}%'
                        : '生成中...',
                    style: TextStyle(
                      fontSize: 32.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              )
            : Text(
                '开始生成',
                style: TextStyle(
                  fontSize: 32.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }
}

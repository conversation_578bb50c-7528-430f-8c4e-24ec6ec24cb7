import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/services/apple_payment_service.dart';
import '../../../core/services/auth_service.dart';
import '../../../controllers/app_controller.dart';
import '../../../shared/widgets/vip_modal.dart';

/// 个人中心页面控制器
class ProfileController extends GetxController {
  // 服务依赖 - 在onInit中初始化（更好的性能和最佳实践）
  late final ApplePaymentService _paymentService;
  late final AuthService _authService;
  late final AppController _appController;
  final Logger _logger = Logger();

  // 服务访问器
  ApplePaymentService get paymentService => _paymentService;
  AuthService get authService => _authService;
  AppController get appController => _appController;

  // 响应式状态
  String get title {
    final token = authService.getTokenFromStorage();
    if (token.isEmpty) return '游客';
    final membershipPeriod = appController.membershipPeriod.value;
    if (membershipPeriod != null && !membershipPeriod.isExpired)
      return '尊贵的VIP';
    return '普通用户';
  }

  @override
  void onInit() {
    super.onInit();
    print('ProfileController onInit called'); // 调试信息

    // 初始化服务依赖
    _paymentService = Get.find<ApplePaymentService>();
    _authService = Get.find<AuthService>();
    _appController = Get.find<AppController>();
  }

  /// 开通VIP会员
  void openVipMembership() {
    _logger.d('open vip card in profile page'); // 调试信息

    // 显示VIP弹窗
    VipModal.show(
      onPurchase: (vipType) async {
        _logger.d('购买成功: $vipType');
        await _appController.refreshMembershipPeriod(isAfterPurchase: true);
      },
    );
  }

  /// 打开用户协议
  void openUserAgreement() {
    _logger.d('打开用户协议');
    Get.toNamed(
      AppConstants.routeWebView,
      arguments: {'title': '用户协议', 'url': AppConstants.userAgreementUrl},
    );
  }

  /// 打开隐私政策
  void openPrivacyPolicy() {
    _logger.d('打开隐私政策');
    Get.toNamed(
      AppConstants.routeWebView,
      arguments: {'title': '隐私政策', 'url': AppConstants.privacyPolicyUrl},
    );
  }

  /// 恢复购买
  void restorePurchase() async {
    _logger.d('恢复购买');
    _paymentService.restorePurchases();
  }
}

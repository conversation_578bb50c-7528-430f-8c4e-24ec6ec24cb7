import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../providers/profile_controller.dart';
import '../widgets/profile_header.dart';
import '../widgets/vip_privilege_card.dart';
import '../widgets/settings_list.dart';

/// 个人中心页面
class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProfileController>(
      builder: (controller) {
        return Scaffold(
          backgroundColor: const Color(0xFFFAFAFA),
          body: Safe<PERSON>rea(
            child: Container(
              color: const Color(0xFFF8F8F8),
              child: Column(
                children: [
                  // 主要内容区域
                  Expanded(
                    child: SingleChildScrollView(
                      child: Padding(
                        padding: EdgeInsets.fromLTRB(32.w, 31.h, 26.w, 0),
                        child: <PERSON>umn(
                          children: [
                            // 导航栏
                            ProfileHeader(
                              title: '个人中心',
                              onBackPressed: () => Get.back(),
                            ),
                            SizedBox(height: 40.w),
                            // VIP特权
                            VipPrivilegeCard(
                              title: controller.title,
                              onVipPressed: controller.openVipMembership,
                            ),
                            SizedBox(height: 40.w),

                            // 设置选项列表
                            SettingsList(
                              onUserAgreementPressed:
                                  controller.openUserAgreement,
                              onPrivacyPolicyPressed:
                                  controller.openPrivacyPolicy,
                              onRestorePurchasePressed:
                                  controller.restorePurchase,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

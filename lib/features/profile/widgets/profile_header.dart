import 'package:cartoon_camera/shared/widgets/back_icon.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 个人中心页面头部组件
class ProfileHeader extends StatelessWidget {
  final String title;
  final VoidCallback onBackPressed;

  const ProfileHeader({
    super.key,
    required this.title,
    required this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // 返回按钮
        BackIcon(),

        // 标题
        Text(
          title,
          style: TextStyle(
            color: const Color(0xFF333333),
            fontSize: 36.sp,
            fontWeight: FontWeight.w500,
            letterSpacing: 1.29.w,
            height: 50.h / 36.sp,
          ),
        ),

        // 占位，保持标题居中
        SizedBox(width: 24.w),
      ],
    );
  }
}

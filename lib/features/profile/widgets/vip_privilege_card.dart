import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../shared/widgets/optimized_image.dart';

/// VIP特权卡片组件
class VipPrivilegeCard extends StatelessWidget {
  final VoidCallback onVipPressed;
  final String title;

  const VipPrivilegeCard({
    super.key,
    required this.onVipPressed,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 692.w,
      decoration: BoxDecoration(
        image: const DecorationImage(
          image: AssetImage('assets/images/profile_card_background.png'),
          fit: BoxFit.cover,
        ),
        borderRadius: BorderRadius.circular(16.r),
      ),
      padding: EdgeInsets.fromLTRB(29.w, 37.h, 28.w, 34.h),
      child: Column(
        children: [
          // 顶部"我的特权"标题
          Row(
            children: [
              Image.asset(
                'assets/images/vip_icon.png',
                width: 38.w,
                height: 33.h,
              ),
              SizedBox(width: 16.w),
              Text(
                title,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 28.sp,
                  fontWeight: FontWeight.w600,
                  height: 40.h / 28.sp,
                ),
              ),
            ],
          ),

          SizedBox(height: 34.h),

          // VIP会员信息和开通按钮
          Container(
            width: 634.w,
            decoration: BoxDecoration(
              image: const DecorationImage(
                image: AssetImage(
                  'assets/images/profile_card_background_inside.png',
                ),
                fit: BoxFit.cover,
              ),
              borderRadius: BorderRadius.circular(12.r),
            ),
            padding: EdgeInsets.fromLTRB(31.w, 38.h, 32.w, 32.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // VIP信息文本
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "VIP会员·尽享专属权益",
                      style: TextStyle(
                        color: const Color(0xFF333333),
                        fontSize: 24.sp,
                        fontWeight: FontWeight.w500,
                        height: 33.h / 24.sp,
                      ),
                    ),
                    SizedBox(height: 19.h),
                    Text(
                      '各种风格图片任意生成 · 无任何广告',
                      style: TextStyle(
                        color: const Color(0xFF999999),
                        fontSize: 20.sp,
                        fontWeight: FontWeight.w500,
                        height: 28.h / 20.sp,
                      ),
                    ),
                  ],
                ),

                // 开通VIP按钮
                GestureDetector(
                  onTap: onVipPressed,
                  child: Container(
                    decoration: BoxDecoration(
                      color: const Color(0xFFFF6D6D),
                      borderRadius: BorderRadius.circular(27.r),
                    ),
                    padding: EdgeInsets.fromLTRB(25.w, 10.h, 25.w, 10.h),
                    child: Text(
                      "开通VIP",
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 24.sp,
                        fontWeight: FontWeight.w500,
                        letterSpacing: 1.3.w,
                        height: 33.h / 24.sp,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

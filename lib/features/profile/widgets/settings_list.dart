import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../shared/widgets/optimized_image.dart';

/// 设置选项列表组件
class SettingsList extends StatelessWidget {
  final VoidCallback onUserAgreementPressed;
  final VoidCallback onPrivacyPolicyPressed;
  final VoidCallback onRestorePurchasePressed;

  const SettingsList({
    super.key,
    required this.onUserAgreementPressed,
    required this.onPrivacyPolicyPressed,
    required this.onRestorePurchasePressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 691.w,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(36.r),
      ),
      padding: EdgeInsets.all(32.w),
      child: Column(
        children: [
          // 用户协议
          _buildSettingsItem(
            iconUrl: 'assets/icons/doc.png',
            iconWidth: 31.w,
            iconHeight: 38.h,
            title: '用户协议',
            onPressed: onUserAgreementPressed,
          ),

          SizedBox(height: 64.h),

          // 隐私协议
          _buildSettingsItem(
            iconUrl: 'assets/icons/key.png',
            iconWidth: 32.w,
            iconHeight: 37.h,
            title: '隐私协议',
            onPressed: onPrivacyPolicyPressed,
          ),

          SizedBox(height: 64.h),

          // 恢复购买
          _buildSettingsItem(
            iconUrl: 'assets/icons/reload.png',
            iconWidth: 32.w,
            iconHeight: 34.h,
            title: '恢复购买',
            onPressed: onRestorePurchasePressed,
          ),
        ],
      ),
    );
  }

  /// 构建设置项
  Widget _buildSettingsItem({
    required String iconUrl,
    required double iconWidth,
    required double iconHeight,
    required String title,
    required VoidCallback onPressed,
  }) {
    return GestureDetector(
      onTap: onPressed,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 左侧图标和标题
          Row(
            children: [
              Image.asset(
                iconUrl,
                width: iconWidth,
                height: iconHeight,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: iconWidth,
                    height: iconHeight,
                    color: const Color(0xFFCCCCCC),
                    child: Icon(
                      Icons.settings,
                      size: iconWidth * 0.6,
                      color: Colors.white,
                    ),
                  );
                },
              ),
              SizedBox(width: 16.w),
              Text(
                title,
                style: TextStyle(
                  color: const Color(0xFF333333),
                  fontSize: 32.sp,
                  fontWeight: FontWeight.w500,
                  height: 45.h / 32.sp,
                ),
              ),
            ],
          ),

          // 右侧箭头
          Icon(
            Icons.arrow_forward_ios,
            size: 30.w,
            color: const Color(0xFF999999),
          ),
        ],
      ),
    );
  }
}

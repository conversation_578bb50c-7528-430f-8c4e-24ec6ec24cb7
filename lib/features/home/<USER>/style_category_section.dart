import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../shared/models/template_response_model.dart';
import '../../../shared/widgets/optimized_image.dart';

/// 风格分类区域组件
class StyleCategorySection extends StatelessWidget {
  final String title;
  final List<TemplateStyleItem> styles;
  final Function(TemplateStyleItem) onStyleTap;
  final VoidCallback onViewAllTap;

  const StyleCategorySection({
    super.key,
    required this.title,
    required this.styles,
    required this.onStyleTap,
    required this.onViewAllTap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 标题栏
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: TextStyle(
                color: const Color(0xFF333333),
                fontSize: 36.sp,
                fontWeight: FontWeight.w500,
                height: 50.h / 36.sp,
              ),
            ),

            GestureDetector(
              onTap: onViewAllTap,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    '全部',
                    style: TextStyle(
                      color: const Color(0xFF999999),
                      fontSize: 36.sp,
                      fontWeight: FontWeight.w500,
                      letterSpacing: 1.29.w,
                      height: 50.h / 36.sp,
                    ),
                  ),
                  SizedBox(width: 8.w),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 36.w,
                    color: const Color(0xFF999999),
                  ),
                  SizedBox(width: 32.w),
                ],
              ),
            ),
          ],
        ),

        SizedBox(height: 32.h),

        // 风格卡片列表
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: styles.asMap().entries.map((entry) {
            final index = entry.key;
            final style = entry.value;
            return _buildStyleCard(style, index);
          }).toList(),
        ),
      ],
    );
  }

  /// 构建风格卡片
  Widget _buildStyleCard(TemplateStyleItem style, int index) {
    return GestureDetector(
      onTap: () => onStyleTap(style),
      child: Container(
        width: 236.w,
        height: 313.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(18.r),
          border: Border.all(color: const Color(0xFF979797), width: 1.w),
        ),
        child: Stack(
          children: [
            // 背景图片
            OptimizedNetworkImage(
              imageUrl: style.template,
              width: 236.w,
              height: 313.w,
              fit: BoxFit.cover,
              borderRadius: BorderRadius.circular(18.r),
            ),
            // 底部文字区域
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.symmetric(
                      horizontal: index == 2 ? 32.w : 46.w,
                      vertical: 13.h,
                    ),
                    decoration: BoxDecoration(
                      // 使用渐变色背景替代图片背景
                      gradient: const LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Color.fromRGBO(0, 0, 0, 0), // rgba(0,0,0,0)
                          Color.fromRGBO(0, 0, 0, 0.8), // rgba(0,0,0,0.8)
                        ],
                        stops: [0.0, 1.0], // 0% 到 100%
                      ),
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(18.r),
                        bottomRight: Radius.circular(18.r),
                      ),
                    ),
                    child: Text(
                      style.style,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 24.sp,
                        fontWeight: FontWeight.normal,
                        height: 33.h / 24.sp,
                      ),
                      textAlign: index == 2 ? TextAlign.center : TextAlign.left,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

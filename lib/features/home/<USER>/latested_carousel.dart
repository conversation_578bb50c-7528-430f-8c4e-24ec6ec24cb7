import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../shared/models/template_response_model.dart';
import '../../../shared/widgets/optimized_image.dart';

class LatestCarousel extends StatefulWidget {
  final List<TemplateStyleItem> templates;
  final Function(TemplateStyleItem) onStyleTap;

  const LatestCarousel({
    super.key,
    required this.templates,
    required this.onStyleTap,
  });

  @override
  State<LatestCarousel> createState() => _LatestCarouselState();
}

class _LatestCarouselState extends State<LatestCarousel> {
  int _currentIndex = 1; // 默认中间卡片为当前选中

  @override
  Widget build(BuildContext context) {
    if (widget.templates.isEmpty) {
      return SizedBox(
        height: 720.w,
        child: const Center(child: CircularProgressIndicator()),
      );
    }

    return SizedBox(
      height: 720.w, // 固定轮播容器高度
      child: CarouselSlider.builder(
        itemCount: widget.templates.length,
        itemBuilder: (context, index, realIndex) {
          final isCenter = index == _currentIndex;
          final template = widget.templates[index];
          return _buildCarouselCard(template: template, isCenter: isCenter);
        },
        options: CarouselOptions(
          height: 682.w,
          viewportFraction: 0.8,
          enlargeCenterPage: true,
          enlargeFactor: 0.25,
          enableInfiniteScroll: true,
          autoPlay: false,
          initialPage: 1,
          scrollDirection: Axis.horizontal,
          pageSnapping: true,
          padEnds: true,
          onPageChanged: (index, reason) {
            setState(() {
              _currentIndex = index;
            });
          },
        ),
      ),
    );
  }

  Widget _buildCarouselCard({
    required TemplateStyleItem template,
    required bool isCenter,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 8.w), // 卡片间距
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.w),
        boxShadow: isCenter
            ? [
                // 中间卡片的增强阴影效果
                BoxShadow(
                  color: const Color(0xFF000000).withValues(alpha: 0.15),
                  offset: Offset(0, 8.w),
                  blurRadius: 60.w,
                  spreadRadius: 4.w,
                ),
                // BoxShadow(
                //   color: const Color(0xFF000000).withValues(alpha: 0.1),
                //   offset: Offset(0, 4.w),
                //   blurRadius: 32.w,
                //   spreadRadius: 0,
                // ),
              ]
            : [
                // 侧边卡片的普通阴影
                BoxShadow(
                  color: const Color(0xFFB6B6B6).withValues(alpha: 0.3),
                  offset: Offset(2.w, 5.w),
                  blurRadius: 16.w,
                ),
              ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12.w),
        child: Column(
          children: [
            // 图片区域
            SizedBox(
              height: 378.w,
              width: double.infinity,
              child: OptimizedNetworkImage(
                imageUrl: template.template.isNotEmpty
                    ? template.template
                    : "https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/df45f31692c9422da2638e5d071a883a_mergeImage.png",
                height: 378.w,
                fit: BoxFit.cover,
              ),
            ),

            // 内容区域 - 使用Expanded自适应剩余空间
            Expanded(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 32.w, vertical: 20.w),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // 标题
                    Flexible(
                      child: Text(
                        template.style,
                        style: TextStyle(
                          color: const Color(0xFF333333),
                          fontSize: 41.w,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),

                    // 副标题
                    Flexible(
                      child: Text(
                        '近期已有10000+制作',
                        style: TextStyle(
                          color: const Color(0xFF999999),
                          fontSize: 30.w,
                          fontWeight: FontWeight.w400,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),

                    // 按钮 - 使用Flexible确保不被截断
                    Flexible(
                      child: Container(
                        width: double.infinity,
                        constraints: BoxConstraints(
                          minHeight: 60.w, // 最小高度
                          maxHeight: 76.w, // 最大高度
                        ),
                        child: TextButton(
                          onPressed: () => widget.onStyleTap(template),
                          style: TextButton.styleFrom(
                            backgroundColor: const Color(0xFFFF6B6B),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(38.w),
                            ),
                          ),
                          child: Text(
                            '立即制作',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 36.w,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

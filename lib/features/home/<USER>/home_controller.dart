import 'package:get/get.dart';
import '../../../shared/models/template_response_model.dart';
import '../../../controllers/app_controller.dart';
import '../../../core/services/image_share_service.dart';

/// 首页控制器
class HomeController extends GetxController {
  late final AppController _appController;

  // 直接从 AppController 获取模板数据，并进行UI层面的处理
  List<TemplateStyleItem> get recommendTemplates =>
      _appController.templateData.value?.recommend ?? [];

  // 经典动漫风格列表（首页显示前3个）
  List<TemplateStyleItem> get animeTemplates =>
      (_appController.templateData.value?.anime ?? []).take(3).toList();

  // 手绘插画风格列表（首页显示前3个）
  List<TemplateStyleItem> get illustrationTemplates =>
      (_appController.templateData.value?.illustrations ?? []).take(3).toList();

  // 数据是否已加载完成 - 只需要模板数据即可
  bool get isDataLoaded {
    final templateLoaded = _appController.templateData.value != null;
    print('HomeController: templateData loaded = $templateLoaded');
    return templateLoaded;
  }

  @override
  void onInit() {
    super.onInit();
    // 获取 AppController 实例
    _appController = Get.find<AppController>();
    // 监听数据变化并更新UI
    _setupDataListener();
  }

  /// 设置数据监听器
  void _setupDataListener() {
    // 监听模板数据变化，自动更新UI
    ever(_appController.templateData, (templateData) {
      print(
        'HomeController: templateData changed, data: ${templateData != null ? "已加载" : "未加载"}',
      );
      if (templateData != null) {
        print('HomeController: 更新首页UI');
        update(['home_content', 'carousel']);
      }
    });
  }

  /// 跳转到风格详情页
  void navigateToStyleDetail(TemplateStyleItem style) {
    Get.toNamed('/detail', arguments: style);
  }

  /// 跳转到我的页面
  void navigateToProfile() {
    Get.toNamed('/profile');
  }

  /// 跳转到全部风格页面
  void navigateToList(String category) {
    Get.toNamed('/list', arguments: {'category': category});
  }

  /// 立即制作
  void startCreation(String styleName) {
    Get.toNamed('/generation', arguments: {'styleName': styleName});
  }

  /// 拍照制作
  void startCameraCreation() {
    Get.toNamed('/generation', arguments: {'source': 'camera'});
  }

  /// 上传制作
  void startUploadCreation() {
    Get.toNamed('/generation', arguments: {'source': 'gallery'});
  }

  /// 分享应用
  void shareApp() async {
    await ImageShareService.shareApp();
  }
}

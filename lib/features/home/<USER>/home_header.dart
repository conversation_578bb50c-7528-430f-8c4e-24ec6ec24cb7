import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../providers/home_controller.dart';
import 'latested_carousel.dart';

/// 首页顶部组件
class HomeHeader extends StatelessWidget {
  const HomeHeader({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<HomeController>();

    return ClipRect(
      child: Stack(
        children: [
          // 背景弧形容器 - 模拟CSS的::after效果
          Positioned(
            left: -150.w, // 相当于CSS的left: -20%
            top: 0,
            child: Container(
              width: 1050.w, // 相当于CSS的width: 140% (750 * 1.4)
              height: MediaQuery.of(context).padding.top + 378.w,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [Color(0xFFFFA4A4), Color(0xFFFF6B6B)],
                ),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.elliptical(525.w, 120.w), // 50% 的弧形
                  bottomRight: Radius.elliptical(525.w, 120.w),
                ),
              ),
            ),
          ),
          // 内容区域
          Container(
            margin: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
            padding: EdgeInsets.only(top: 32.w),
            width: 750.w,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 顶部标题栏
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 32.w),
                  child: _HeaderTitleBar(controller: controller),
                ),
                SizedBox(height: 80.w),
                // 最近上新区域
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 32.w),
                  child: Row(
                    children: [
                      _RecentUpdatesLabel(),
                      _DividerLine(),
                      ...List.generate(3, (index) {
                        return [
                          if (index > 0) SizedBox(width: 24.w),
                          _NumberCircle(number: index + 1),
                        ];
                      }).expand((widgets) => widgets),
                    ],
                  ),
                ),
                SizedBox(height: 40.w),
                GetBuilder<HomeController>(
                  id: 'carousel',
                  builder: (controller) => LatestCarousel(
                    templates: controller.recommendTemplates,
                    onStyleTap: controller.navigateToStyleDetail,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// 顶部标题栏组件
class _HeaderTitleBar extends StatelessWidget {
  final HomeController controller;

  const _HeaderTitleBar({required this.controller});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // 应用标题
        Image.asset("assets/images/home_title.png", width: 228.w),
        // 我的按钮
        _ProfileButton(onTap: controller.navigateToProfile),
      ],
    );
  }
}

/// 个人资料按钮组件
class _ProfileButton extends StatelessWidget {
  final VoidCallback onTap;

  const _ProfileButton({required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 7.h),
        decoration: BoxDecoration(
          color: _HomeHeaderColors.buttonBackground,
          borderRadius: BorderRadius.circular(36.r),
          border: Border.all(color: _HomeHeaderColors.buttonBorder, width: 3.w),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              "assets/images/home_star.png",
              width: 30.w,
              height: 30.w,
              errorBuilder: (context, error, stackTrace) {
                return Icon(Icons.star, size: 30.w, color: Colors.white);
              },
            ),
            SizedBox(width: 8.w),
            Text("我的", style: _HomeHeaderStyles.profileButtonTextStyle),
          ],
        ),
      ),
    );
  }
}

/// 最近上新标签组件
class _RecentUpdatesLabel extends StatelessWidget {
  const _RecentUpdatesLabel();

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 62.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(31.r),
        color: _HomeHeaderColors.labelBackground,
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 30.w),
        child: Center(
          child: Text("最近上新", style: _HomeHeaderStyles.recentUpdatesTextStyle),
        ),
      ),
    );
  }
}

/// 分割线组件
class _DividerLine extends StatelessWidget {
  const _DividerLine();

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 276.w,
      height: 1,
      color: _HomeHeaderColors.dividerColor,
    );
  }
}

/// 数字圆圈组件
class _NumberCircle extends StatelessWidget {
  final int number;

  const _NumberCircle({required this.number});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 50.w,
      height: 50.w,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: _HomeHeaderColors.circleBackground,
      ),
      child: Center(
        child: Text(
          number.toString(),
          style: _HomeHeaderStyles.numberCircleTextStyle,
        ),
      ),
    );
  }
}

/// 首页头部颜色
class _HomeHeaderColors {
  static final Color buttonBackground = Colors.white.withValues(alpha: 0.4);
  static final Color buttonBorder = Colors.white.withValues(alpha: 0.85);
  static final Color labelBackground = const Color.fromARGB(15, 255, 255, 255);
  static final Color circleBackground = const Color.fromARGB(15, 255, 255, 255);
  static const Color dividerColor = Color(0xFFFFA1A1);
}

/// 首页头部样式
class _HomeHeaderStyles {
  static TextStyle get profileButtonTextStyle => TextStyle(
    color: Colors.white,
    fontSize: 32.w,
    fontWeight: FontWeight.w500,
    height: 45.w / 32.w,
  );

  static TextStyle get recentUpdatesTextStyle => TextStyle(
    color: Colors.white,
    fontSize: 36.w,
    fontWeight: FontWeight.w700,
  );

  static TextStyle get numberCircleTextStyle => TextStyle(
    color: Colors.white,
    fontSize: 28.w,
    fontWeight: FontWeight.w500,
  );
}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../providers/home_controller.dart';
import '../widgets/home_header.dart';
import '../widgets/style_category_section.dart';

/// 首页
class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeController>(
      id: 'home_content',
      builder: (controller) {
        return Scaffold(
          backgroundColor: Colors.white,
          floatingActionButton: FloatingActionButton(
            onPressed: controller.shareApp,
            backgroundColor: Colors.transparent,
            elevation: 0,
            child: Image.asset(
              "assets/images/share_app_icon.png",
              width: 80,
              height: 80,
              fit: BoxFit.contain,
            ),
          ),
          floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
          body: !controller.isDataLoaded
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                  child: Column(
                    children: [
                      // 顶部区域
                      const HomeHeader(),

                      // 主要内容区域
                      Padding(
                        padding: EdgeInsets.only(
                          left: 32.w,
                          top: 48.w,
                          bottom: 48.w,
                        ),
                        child: Column(
                          children: [
                            // 经典动漫区域
                            StyleCategorySection(
                              title: '经典动漫',
                              styles: controller.animeTemplates,
                              onStyleTap: controller.navigateToStyleDetail,
                              onViewAllTap: () =>
                                  controller.navigateToList('经典动漫'),
                            ),

                            SizedBox(height: 48.h),

                            // 手绘插画区域
                            StyleCategorySection(
                              title: '手绘插画',
                              styles: controller.illustrationTemplates,
                              onStyleTap: controller.navigateToStyleDetail,
                              onViewAllTap: () =>
                                  controller.navigateToList('手绘插画'),
                            ),

                            SizedBox(height: 80.h),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
        );
      },
    );
  }
}

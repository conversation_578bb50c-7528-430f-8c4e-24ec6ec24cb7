import 'dart:async';
import 'dart:io';
import 'package:cartoon_camera/controllers/app_controller.dart';
import 'package:cartoon_camera/core/services/auth_service.dart';
import 'package:cartoon_camera/shared/widgets/login_modal.dart';
import 'package:cartoon_camera/shared/widgets/vip_modal.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:logger/logger.dart';
import '../../../core/services/apple_payment_service.dart';
import '../../../core/services/permission_service.dart';
import '../../../core/services/oss_service.dart';
import '../../../services/api_service.dart';
import '../../../shared/models/template_response_model.dart';

/// 创建类型枚举
enum CreationType { camera, gallery }

/// 风格详情页面控制器
class DetailController extends GetxController {
  final Logger _logger = Logger();
  AppController get appController => Get.find<AppController>();

  // 支付服务 - 使用可空字段避免重复初始化
  ApplePaymentService? _paymentService;
  ApplePaymentService get paymentService =>
      _paymentService ??= Get.find<ApplePaymentService>();
  // app controller
  final AppController _appController = Get.find<AppController>();

  // API服务 - 使用可空字段避免重复初始化
  ApiService? _apiService;
  ApiService get apiService => _apiService ??= Get.find<ApiService>();

  // OSS服务 - 使用可空字段避免重复初始化
  OssService? _ossService;
  OssService get ossService => _ossService ??= Get.find<OssService>();

  // 认证服务 - 使用可空字段避免重复初始化
  AuthService? _authService;
  AuthService get authService => _authService ??= Get.find<AuthService>();

  // 图片选择器
  final ImagePicker _imagePicker = ImagePicker();

  // 风格名称
  final _styleName = ''.obs;
  String get styleName => _styleName.value;

  // 风格图片URL
  final _styleImageUrl = ''.obs;
  String get styleImageUrl => _styleImageUrl.value;

  // 风格描述
  final _styleDescription = '图像生成提示：\n为保证生成效果，请确保上传照片的清晰度'.obs;
  String get styleDescription => _styleDescription.value;

  @override
  void onInit() {
    super.onInit();

    // 获取传入的参数
    final arguments = Get.arguments;
    _logger.d('Arguments received: $arguments');

    if (arguments != null && arguments is TemplateStyleItem) {
      // 直接接收 TemplateStyleItem 对象
      _styleName.value = arguments.style;
      _styleImageUrl.value = arguments.template;
      _logger.d('Style name set to: ${_styleName.value}');
      _logger.d('Style image URL set to: ${_styleImageUrl.value}');
    } else {
      _logger.w('Invalid arguments received: $arguments');
      // 设置默认值
      _styleName.value = '未知风格';
      _styleImageUrl.value =
          'https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/1182a8bec9db4e9bbbec45525f418a82_mergeImage.png';
    }
  }

  /// 拍照制作和上传制作
  Future<void> startCreation(CreationType type) async {
    try {
      // 1. 检查登录状态
      if (!await _checkLoginStatus(type)) return;
      // 2. 检查会员状态
      if (!await _checkMembershipStatus()) return;

      // 3. 检查使用次数
      if (!await _checkUsageLimit()) return;

      // 4. 根据type选择拍照还是从相册选择，获取照片
      final imageFile = await _pickImage(type);
      if (imageFile == null) return;

      // 5. 计算照片比例
      final aspectRatio = await _calculateAspectRatio(imageFile);

      // 6. 跳转到generation页面
      Get.toNamed(
        '/generation',
        arguments: {
          'imageFile': imageFile,
          'aspectRatio': aspectRatio,
          'styleName': styleName,
        },
      );
    } catch (e) {
      _logger.e('创建过程失败: $e');
      Get.snackbar('错误', '创建失败，请重试');
    }
  }

  /// 检查登录状态
  Future<bool> _checkLoginStatus(CreationType type) async {
    final token = authService.getTokenFromStorage();
    _logger.d('storage token: $token');
    if (token.isEmpty) {
      _logger.d('未登录，显示登录弹窗');
      LoginModal.show(
        onLoginSuccess: (token) {
          _logger.d('登录成功: $token');
          // 登录成功后继续执行创建流程
          _continueCreationAfterLogin(type);
        },
      );
      return false;
    }
    return true;
  }

  /// 登录成功后继续执行创建流程
  Future<void> _continueCreationAfterLogin(CreationType type) async {
    try {
      // 2. 检查会员状态
      if (!await _checkMembershipStatus()) return;

      // 3. 检查使用次数
      if (!await _checkUsageLimit()) return;

      // 4. 根据type选择拍照还是从相册选择，获取照片
      final imageFile = await _pickImage(type);
      if (imageFile == null) return;

      // 5. 计算照片比例
      final aspectRatio = await _calculateAspectRatio(imageFile);

      // 6. 跳转到generation页面
      Get.toNamed(
        '/generation',
        arguments: {
          'imageFile': imageFile,
          'aspectRatio': aspectRatio,
          'styleName': styleName,
        },
      );
    } catch (e) {
      _logger.e('创建流程失败: $e');
      Get.snackbar('错误', '创建失败，请重试');
    }
  }

  /// 检查会员状态
  Future<bool> _checkMembershipStatus() async {
    try {
      final membershipPeriod = _appController.membershipPeriod.value;
      _logger.d('会员状态检查中...${membershipPeriod?.toString() ?? "无会员"}');
      final isVip = membershipPeriod != null;

      if (!isVip) {
        _logger.d('非VIP用户，显示VIP弹窗');
        VipModal.show(
          onPurchase: (vipType) async {
            _logger.d('购买成功: $vipType');
            await _appController.refreshMembershipPeriod(isAfterPurchase: true);
          },
        );
        return false;
      }
      return true;
    } catch (e) {
      _logger.e('检查会员状态失败: $e');
      return false;
    }
  }

  /// 检查使用次数限制
  Future<bool> _checkUsageLimit() async {
    try {
      final membershipPeriod = _appController.membershipPeriod.value;
      final usageCount = await apiService.getUsageCount({
        'startTime': membershipPeriod!.startTime,
        'endTime': membershipPeriod.endTime,
      });
      final productId = membershipPeriod.productId;
      final productInfoList = _appController.productInfo.value;

      // 从产品列表中安全地找到对应的产品信息
      int limit = 1; // 默认限制
      if (productInfoList != null) {
        try {
          final currentProduct = productInfoList.firstWhere(
            (product) => product.productIdentifier == productId,
          );
          limit = currentProduct.limit;
          _logger.d('找到产品 $productId 的限制: $limit');
        } catch (e) {
          _logger.w('未找到产品ID为 $productId 的产品信息，使用默认限制: $limit');
        }
      } else {
        _logger.w('产品信息列表为空，使用默认限制: $limit');
      }

      _logger.d('当前使用次数: $usageCount, 限制: $limit');

      if (usageCount >= limit) {
        Get.snackbar(
          '提示',
          '当前排队人数过多，请稍后重试',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange,
          colorText: Colors.white,
        );
        return false;
      }
      return true;
    } catch (e) {
      _logger.e('检查使用次数失败: $e');
      return false;
    }
  }

  /// 根据类型选择图片
  Future<File?> _pickImage(CreationType type) async {
    switch (type) {
      case CreationType.camera:
        return await _pickImageFromCamera();
      case CreationType.gallery:
        return await _pickImageFromGallery();
    }
  }

  /// 计算图片宽高比并返回对应的比例字符串
  /// 使用内存友好的方式，只读取图片头部信息
  Future<String> _calculateAspectRatio(File imageFile) async {
    try {
      final imageInfo = await _getImageDimensions(imageFile);

      if (imageInfo == null) {
        _logger.w('无法获取图片尺寸，使用默认比例');
        return '1:1';
      }

      final aspectRatio = imageInfo.width / imageInfo.height;
      _logger.d(
        '图片尺寸: ${imageInfo.width}x${imageInfo.height}, 宽高比: $aspectRatio',
      );

      // 宽高比计算逻辑：大于1.25用3:2，小于1.25大于0.75用1:1，小于0.75用2:3
      if (aspectRatio > 1.25) {
        return '3:2';
      } else if (aspectRatio > 0.75) {
        return '1:1';
      } else {
        return '2:3';
      }
    } catch (e) {
      _logger.e('计算图片比例失败: $e');
      return '1:1'; // 默认返回1:1
    }
  }

  /// 内存友好的方式获取图片尺寸
  /// 使用 Flutter 的 Image.file 来获取图片信息，不会加载完整图片到内存
  Future<({int width, int height})?> _getImageDimensions(File imageFile) async {
    try {
      // 使用 Completer 来等待图片加载完成
      final completer = Completer<({int width, int height})?>();

      // 创建一个 Image widget 来获取图片信息
      final image = Image.file(imageFile);
      final imageStream = image.image.resolve(const ImageConfiguration());

      late ImageStreamListener listener;
      listener = ImageStreamListener(
        (ImageInfo info, bool synchronousCall) {
          // 获取到图片信息后立即完成
          final width = info.image.width;
          final height = info.image.height;

          // 释放图片资源
          info.image.dispose();

          // 移除监听器
          imageStream.removeListener(listener);

          completer.complete((width: width, height: height));
        },
        onError: (exception, stackTrace) {
          imageStream.removeListener(listener);
          completer.complete(null);
        },
      );

      imageStream.addListener(listener);

      // 设置超时，避免无限等待
      return await completer.future.timeout(
        const Duration(seconds: 5),
        onTimeout: () {
          imageStream.removeListener(listener);
          return null;
        },
      );
    } catch (e) {
      _logger.e('获取图片尺寸失败: $e');
      return null;
    }
  }

  /// 从相机拍照
  Future<File?> _pickImageFromCamera() async {
    try {
      // 检查相机权限
      final hasPermission = await PermissionService.requestCameraPermission();
      if (!hasPermission) {
        PermissionService.showPermissionDeniedMessage('相机');
        return null;
      }

      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        maxWidth: 2048,
        maxHeight: 2048,
        imageQuality: 90,
      );

      return image != null ? File(image.path) : null;
    } catch (e) {
      Get.snackbar('错误', '拍照失败: $e');
      return null;
    }
  }

  /// 从相册选择
  Future<File?> _pickImageFromGallery() async {
    try {
      // 检查相册权限
      final hasPermission = await PermissionService.requestPhotosPermission();
      if (!hasPermission) {
        PermissionService.showPermissionDeniedMessage('相册');
        return null;
      }

      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 2048,
        maxHeight: 2048,
        imageQuality: 90,
      );

      return image != null ? File(image.path) : null;
    } catch (e) {
      Get.snackbar('错误', '选择图片失败: $e');
      return null;
    }
  }
}

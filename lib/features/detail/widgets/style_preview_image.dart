import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 风格预览图片组件
class StylePreviewImage extends StatelessWidget {
  final String imageUrl;

  const StylePreviewImage({super.key, required this.imageUrl});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 686.w,
      height: 911.w, // 减小高度避免溢出
      margin: EdgeInsets.only(top: 16.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
        image: DecorationImage(
          image: NetworkImage(imageUrl),
          fit: BoxFit.cover,
        ),
      ),
    );
  }
}

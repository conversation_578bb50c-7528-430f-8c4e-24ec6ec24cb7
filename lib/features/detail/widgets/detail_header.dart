import 'package:cartoon_camera/shared/widgets/back_icon.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 详情页头部组件
class DetailHeader extends StatelessWidget {
  final String title;
  final VoidCallback onBackPressed;

  const DetailHeader({
    super.key,
    required this.title,
    required this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(32.w, 31.h, 32.w, 56.h),
      child: Row(
        children: [
          // 返回按钮
          BackIcon(),

          // 标题
          Expanded(
            child: Text(
              title,
              style: TextStyle(
                color: const Color(0xFF333333),
                fontSize: 36.sp,
                fontWeight: FontWeight.w500,
                letterSpacing: 1.29.w,
                height: 50.h / 36.sp,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 底部操作按钮组件
class ActionButtons extends StatelessWidget {
  final VoidCallback onCameraPressed;
  final VoidCallback onUploadPressed;

  const ActionButtons({
    super.key,
    required this.onCameraPressed,
    required this.onUploadPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.fromLTRB(44.w, 38.h, 44.w, 47.h),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: const Color.fromRGBO(0, 0, 0, 0.5),
            blurRadius: 4.r,
            offset: const Offset(0, 0),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 拍照制作按钮
          GestureDetector(
            onTap: onCameraPressed,
            child: Container(
              padding: EdgeInsets.fromLTRB(75.w, 16.h, 74.w, 16.h),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(46.r),
                border: Border.all(color: const Color(0xFFFF6B6B), width: 1.w),
              ),
              child: Text(
                '拍照制作',
                style: TextStyle(
                  color: const Color(0xFFFF6B6B),
                  fontSize: 41.sp,
                  fontWeight: FontWeight.w500,
                  letterSpacing: 0.95.w,
                  height: 57.h / 41.sp,
                ),
              ),
            ),
          ),

          // 上传制作按钮
          GestureDetector(
            onTap: onUploadPressed,
            child: Container(
              padding: EdgeInsets.fromLTRB(76.w, 17.h, 75.w, 17.h),
              decoration: BoxDecoration(
                color: const Color(0xFFFF6B6B),
                borderRadius: BorderRadius.circular(46.r),
              ),
              child: Text(
                '上传制作',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 41.sp,
                  fontWeight: FontWeight.w500,
                  letterSpacing: 0.95.w,
                  height: 57.h / 41.sp,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

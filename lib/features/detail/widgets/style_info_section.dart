import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 风格信息描述组件
class StyleInfoSection extends StatelessWidget {
  final String styleName;
  final String description;

  const StyleInfoSection({
    super.key,
    required this.styleName,
    required this.description,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(32.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 风格名称
          Text(
            styleName,
            style: TextStyle(
              color: const Color(0xFF333333),
              fontSize: 36.sp,
              fontWeight: FontWeight.w500,
              letterSpacing: 1.29.w,
              height: 50.h / 36.sp,
            ),
          ),

          SizedBox(height: 14.h),

          // 描述文本
          Text(
            description,
            style: TextStyle(
              color: const Color(0xFF999999),
              fontSize: 30.sp,
              fontWeight: FontWeight.w500,
              letterSpacing: 1.5.w,
              height: 45.h / 30.sp,
            ),
          ),
        ],
      ),
    );
  }
}

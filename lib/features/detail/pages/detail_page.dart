import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../providers/detail_controller.dart';
import '../widgets/detail_header.dart';
import '../widgets/style_preview_image.dart';
import '../widgets/style_info_section.dart';
import '../widgets/action_buttons.dart';

/// 风格详情页面
class DetailPage extends StatelessWidget {
  const DetailPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<DetailController>(
      builder: (controller) {
        return Scaffold(
          backgroundColor: Colors.white,
          body: SafeArea(
            child: Column(
              children: [
                // 导航栏
                DetailHeader(
                  title: controller.styleName,
                  onBackPressed: () => Get.back(),
                ),

                // 主要内容区域 - 使用SingleChildScrollView避免溢出
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        // 风格预览图片
                        StylePreviewImage(imageUrl: controller.styleImageUrl),

                        // 风格信息描述
                        StyleInfoSection(
                          styleName: controller.styleName,
                          description: controller.styleDescription,
                        ),
                      ],
                    ),
                  ),
                ),

                // 底部操作按钮 - 固定在底部
                ActionButtons(
                  onCameraPressed: () =>
                      controller.startCreation(CreationType.camera),
                  onUploadPressed: () =>
                      controller.startCreation(CreationType.gallery),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

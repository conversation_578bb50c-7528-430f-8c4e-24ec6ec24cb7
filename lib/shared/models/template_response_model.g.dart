// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'template_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ApiResponse<T> _$ApiResponseFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) =>
    ApiResponse<T>(
      code: (json['code'] as num).toInt(),
      message: json['message'] as String,
      data: _$nullableGenericFromJson(json['data'], fromJsonT),
      success: json['success'] as bool,
    );

Map<String, dynamic> _$ApiResponseToJson<T>(
  ApiResponse<T> instance,
  Object? Function(T value) toJsonT,
) =>
    <String, dynamic>{
      'code': instance.code,
      'message': instance.message,
      'data': _$nullableGenericToJson(instance.data, toJsonT),
      'success': instance.success,
    };

T? _$nullableGenericFromJson<T>(
  Object? input,
  T Function(Object? json) fromJson,
) =>
    input == null ? null : fromJson(input);

Object? _$nullableGenericToJson<T>(
  T? input,
  Object? Function(T value) toJson,
) =>
    input == null ? null : toJson(input);

TemplateStyleItem _$TemplateStyleItemFromJson(Map<String, dynamic> json) =>
    TemplateStyleItem(
      style: json['style'] as String,
      template: json['template'] as String,
    );

Map<String, dynamic> _$TemplateStyleItemToJson(TemplateStyleItem instance) =>
    <String, dynamic>{
      'style': instance.style,
      'template': instance.template,
    };

TemplateData _$TemplateDataFromJson(Map<String, dynamic> json) => TemplateData(
      recommend: (json['recommend'] as List<dynamic>)
          .map((e) => TemplateStyleItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      anime: (json['anime'] as List<dynamic>)
          .map((e) => TemplateStyleItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      illustrations: (json['illustrations'] as List<dynamic>)
          .map((e) => TemplateStyleItem.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$TemplateDataToJson(TemplateData instance) =>
    <String, dynamic>{
      'recommend': instance.recommend,
      'anime': instance.anime,
      'illustrations': instance.illustrations,
    };

OSSConfigData _$OSSConfigDataFromJson(Map<String, dynamic> json) =>
    OSSConfigData(
      bucket: json['bucket'] as String,
      endpoint: json['endpoint'] as String,
      cdnHost: json['cdnHost'] as String,
    );

Map<String, dynamic> _$OSSConfigDataToJson(OSSConfigData instance) =>
    <String, dynamic>{
      'bucket': instance.bucket,
      'endpoint': instance.endpoint,
      'cdnHost': instance.cdnHost,
    };

STSInfoData _$STSInfoDataFromJson(Map<String, dynamic> json) => STSInfoData(
      statusCode: (json['StatusCode'] as num).toInt(),
      accessKeyId: json['AccessKeyId'] as String,
      accessKeySecret: json['AccessKeySecret'] as String,
      securityToken: json['SecurityToken'] as String,
      expiration: json['Expiration'] as String,
    );

Map<String, dynamic> _$STSInfoDataToJson(STSInfoData instance) =>
    <String, dynamic>{
      'StatusCode': instance.statusCode,
      'AccessKeyId': instance.accessKeyId,
      'AccessKeySecret': instance.accessKeySecret,
      'SecurityToken': instance.securityToken,
      'Expiration': instance.expiration,
    };

ProductDiscount _$ProductDiscountFromJson(Map<String, dynamic> json) =>
    ProductDiscount(
      localizedPrice: json['localizedPrice'] as String,
      description: json['description'] as String,
    );

Map<String, dynamic> _$ProductDiscountToJson(ProductDiscount instance) =>
    <String, dynamic>{
      'localizedPrice': instance.localizedPrice,
      'description': instance.description,
    };

ProductInfo _$ProductInfoFromJson(Map<String, dynamic> json) => ProductInfo(
      productIdentifier: json['productIdentifier'] as String,
      localizedTitle: json['localizedTitle'] as String,
      localizedPrice: json['localizedPrice'] as String,
      discounts: (json['discounts'] as List<dynamic>)
          .map((e) => ProductDiscount.fromJson(e as Map<String, dynamic>))
          .toList(),
      limit: (json['limit'] as num).toInt(),
    );

Map<String, dynamic> _$ProductInfoToJson(ProductInfo instance) =>
    <String, dynamic>{
      'productIdentifier': instance.productIdentifier,
      'localizedTitle': instance.localizedTitle,
      'localizedPrice': instance.localizedPrice,
      'discounts': instance.discounts,
      'limit': instance.limit,
    };

RatingGuideData _$RatingGuideDataFromJson(Map<String, dynamic> json) =>
    RatingGuideData(
      need: json['need'] as bool,
      type: json['type'] as String,
    );

Map<String, dynamic> _$RatingGuideDataToJson(RatingGuideData instance) =>
    <String, dynamic>{
      'need': instance.need,
      'type': instance.type,
    };

ImageGenerationRequest _$ImageGenerationRequestFromJson(
        Map<String, dynamic> json) =>
    ImageGenerationRequest(
      url: json['url'] as String,
      style: json['style'] as String,
      size: json['size'] as String,
    );

Map<String, dynamic> _$ImageGenerationRequestToJson(
        ImageGenerationRequest instance) =>
    <String, dynamic>{
      'url': instance.url,
      'style': instance.style,
      'size': instance.size,
    };

LoginData _$LoginDataFromJson(Map<String, dynamic> json) => LoginData(
      accessToken: json['accessToken'] as String,
      email: json['email'] as String?,
      username: json['username'] as String?,
    );

Map<String, dynamic> _$LoginDataToJson(LoginData instance) => <String, dynamic>{
      'accessToken': instance.accessToken,
      'email': instance.email,
      'username': instance.username,
    };

import 'package:json_annotation/json_annotation.dart';

part 'template_response_model.g.dart';

/// 统一响应
@JsonSerializable(genericArgumentFactories: true)
class ApiResponse<T> {
  final int code;
  final String message;
  final T? data;
  final bool success;

  ApiResponse({
    required this.code,
    required this.message,
    this.data,
    required this.success,
  });

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) => _$ApiResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$ApiResponseToJson(this, toJsonT);
}

/// 模板风格项
@JsonSerializable()
class TemplateStyleItem {
  final String style;
  final String template;

  TemplateStyleItem({required this.style, required this.template});

  factory TemplateStyleItem.fromJson(Map<String, dynamic> json) =>
      _$TemplateStyleItemFromJson(json);

  Map<String, dynamic> toJson() => _$TemplateStyleItemToJson(this);
}

/// 模板数据
@JsonSerializable()
class TemplateData {
  final List<TemplateStyleItem> recommend;
  final List<TemplateStyleItem> anime;
  final List<TemplateStyleItem> illustrations;

  TemplateData({
    required this.recommend,
    required this.anime,
    required this.illustrations,
  });

  factory TemplateData.fromJson(Map<String, dynamic> json) =>
      _$TemplateDataFromJson(json);

  Map<String, dynamic> toJson() => _$TemplateDataToJson(this);
}

/// OSS配置数据
@JsonSerializable()
class OSSConfigData {
  final String bucket;
  final String endpoint;
  final String cdnHost;

  OSSConfigData({
    required this.bucket,
    required this.endpoint,
    required this.cdnHost,
  });

  factory OSSConfigData.fromJson(Map<String, dynamic> json) =>
      _$OSSConfigDataFromJson(json);

  Map<String, dynamic> toJson() => _$OSSConfigDataToJson(this);
}

/// OSS配置响应
typedef OSSConfigResponse = ApiResponse<OSSConfigData>;

/// STS临时凭证数据
@JsonSerializable()
class STSInfoData {
  @JsonKey(name: 'StatusCode')
  final int statusCode;
  @JsonKey(name: 'AccessKeyId')
  final String accessKeyId;
  @JsonKey(name: 'AccessKeySecret')
  final String accessKeySecret;
  @JsonKey(name: 'SecurityToken')
  final String securityToken;
  @JsonKey(name: 'Expiration')
  final String expiration;

  STSInfoData({
    required this.statusCode,
    required this.accessKeyId,
    required this.accessKeySecret,
    required this.securityToken,
    required this.expiration,
  });

  factory STSInfoData.fromJson(Map<String, dynamic> json) =>
      _$STSInfoDataFromJson(json);

  Map<String, dynamic> toJson() => _$STSInfoDataToJson(this);
}

/// STS信息响应
typedef STSInfoResponse = ApiResponse<STSInfoData>;

/// 产品折扣信息
@JsonSerializable()
class ProductDiscount {
  final String localizedPrice;
  final String description;

  ProductDiscount({required this.localizedPrice, required this.description});

  factory ProductDiscount.fromJson(Map<String, dynamic> json) =>
      _$ProductDiscountFromJson(json);

  Map<String, dynamic> toJson() => _$ProductDiscountToJson(this);
}

/// 产品信息数据
@JsonSerializable()
class ProductInfo {
  final String productIdentifier;
  final String localizedTitle;
  final String localizedPrice;
  final List<ProductDiscount> discounts;
  final int limit;

  ProductInfo({
    required this.productIdentifier,
    required this.localizedTitle,
    required this.localizedPrice,
    required this.discounts,
    required this.limit,
  });

  factory ProductInfo.fromJson(Map<String, dynamic> json) =>
      _$ProductInfoFromJson(json);

  Map<String, dynamic> toJson() => _$ProductInfoToJson(this);
}

/// 产品信息响应
typedef ProductInfoResponse = ApiResponse<List<ProductInfo>>;

/// 评分引导数据
@JsonSerializable()
class RatingGuideData {
  final bool need;
  final String type;

  RatingGuideData({required this.need, required this.type});

  factory RatingGuideData.fromJson(Map<String, dynamic> json) =>
      _$RatingGuideDataFromJson(json);

  Map<String, dynamic> toJson() => _$RatingGuideDataToJson(this);
}

/// 评分引导响应
typedef RatingGuideResponse = ApiResponse<RatingGuideData>;

/// 图片生成请求参数
@JsonSerializable()
class ImageGenerationRequest {
  /// 图片URL
  final String url;

  /// 风格样式
  final String style;

  /// 图片尺寸比例
  final String size;

  ImageGenerationRequest({
    required this.url,
    required this.style,
    required this.size,
  });

  factory ImageGenerationRequest.fromJson(Map<String, dynamic> json) =>
      _$ImageGenerationRequestFromJson(json);

  Map<String, dynamic> toJson() => _$ImageGenerationRequestToJson(this);
}

/// 登录响应数据
@JsonSerializable()
class LoginData {
  /// 访问令牌
  final String accessToken;

  /// 用户邮箱（可能为空）
  final String? email;

  /// 用户名（可能为空）
  final String? username;

  LoginData({required this.accessToken, this.email, this.username});

  factory LoginData.fromJson(Map<String, dynamic> json) =>
      _$LoginDataFromJson(json);

  Map<String, dynamic> toJson() => _$LoginDataToJson(this);
}

/// 登录响应
typedef LoginResponse = ApiResponse<LoginData>;

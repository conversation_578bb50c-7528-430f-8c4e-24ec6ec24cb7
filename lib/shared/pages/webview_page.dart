import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

/// 通用WebView页面
class WebViewPage extends StatefulWidget {
  const WebViewPage({super.key});

  @override
  State<WebViewPage> createState() => _WebViewPageState();
}

class _WebViewPageState extends State<WebViewPage> {
  late final WebViewController _controller;
  bool _isLoading = true;
  String _title = '';
  String _url = '';

  @override
  void initState() {
    super.initState();

    // 从路由参数获取标题和URL
    final arguments = Get.arguments as Map<String, dynamic>?;
    _title = arguments?['title'] ?? 'WebView';
    _url = arguments?['url'] ?? '';

    // 初始化WebView控制器
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // 更新加载进度
          },
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
            });
          },
          onWebResourceError: (WebResourceError error) {
            // 处理加载错误
            Get.snackbar(
              '加载失败',
              '页面加载失败，请检查网络连接',
              snackPosition: SnackPosition.TOP,
            );
          },
        ),
      );

    // 加载URL
    if (_url.isNotEmpty) {
      _controller.loadRequest(Uri.parse(_url));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: const Color(0xFF333333),
            size: 44.w,
          ),
          onPressed: () => Get.back(),
        ),
        title: Text(
          _title,
          style: TextStyle(
            color: const Color(0xFF333333),
            fontSize: 36.w,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
        actions: [
          // 刷新按钮
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: const Color(0xFF333333),
              size: 44.w,
            ),
            onPressed: () {
              _controller.reload();
            },
          ),
        ],
      ),
      body: Stack(
        children: [
          // WebView内容
          WebViewWidget(controller: _controller),

          // 加载指示器
          if (_isLoading)
            Container(
              color: Colors.white,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        const Color(0xFFFF6D6D),
                      ),
                    ),
                    SizedBox(height: 16.h),
                    Text(
                      '正在加载...',
                      style: TextStyle(
                        color: const Color(0xFF666666),
                        fontSize: 14.sp,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}

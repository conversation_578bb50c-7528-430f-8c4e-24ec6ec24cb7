import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../core/constants/app_colors.dart';
import '../../core/services/image_save_service.dart';
import '../../core/services/image_share_service.dart';
import '../../core/config/image_cache_config.dart';
import 'loading_widget.dart';

/// 优化的网络图片组件
class OptimizedNetworkImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final BorderRadius? borderRadius;
  final Widget? placeholder;
  final Widget? errorWidget;
  final Duration fadeInDuration;
  final Duration placeholderFadeInDuration;

  const OptimizedNetworkImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.placeholder,
    this.errorWidget,
    this.fadeInDuration = const Duration(milliseconds: 300),
    this.placeholderFadeInDuration = const Duration(milliseconds: 300),
  });

  @override
  Widget build(BuildContext context) {
    Widget imageWidget = CachedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      fadeInDuration: fadeInDuration,
      placeholderFadeInDuration: placeholderFadeInDuration,
      placeholder: (context, url) => placeholder ?? _buildDefaultPlaceholder(),
      errorWidget: (context, url, error) => errorWidget ?? _buildDefaultError(),
      memCacheWidth: width?.toInt(),
      memCacheHeight: height?.toInt(),
      cacheManager: ImageCacheConfig.customCacheManager, // 使用自定义缓存管理器
    );

    if (borderRadius != null) {
      imageWidget = ClipRRect(borderRadius: borderRadius!, child: imageWidget);
    }

    return imageWidget;
  }

  Widget _buildDefaultPlaceholder() {
    return Container(
      width: width,
      height: height,
      color: AppColors.background,
      child: const Center(child: LoadingWidget()),
    );
  }

  Widget _buildDefaultError() {
    return Container(
      width: width,
      height: height,
      color: AppColors.background,
      child: Icon(
        Icons.broken_image_outlined,
        size: 32.w,
        color: AppColors.textSecondary,
      ),
    );
  }
}

/// 圆形头像组件
class CircleAvatar extends StatelessWidget {
  final String? imageUrl;
  final double radius;
  final Widget? placeholder;
  final Widget? errorWidget;
  final Color? backgroundColor;

  const CircleAvatar({
    super.key,
    this.imageUrl,
    required this.radius,
    this.placeholder,
    this.errorWidget,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    if (imageUrl == null || imageUrl!.isEmpty) {
      return _buildDefaultAvatar();
    }

    return ClipOval(
      child: OptimizedNetworkImage(
        imageUrl: imageUrl!,
        width: radius * 2,
        height: radius * 2,
        fit: BoxFit.cover,
        placeholder: placeholder ?? _buildDefaultAvatar(),
        errorWidget: errorWidget ?? _buildDefaultAvatar(),
      ),
    );
  }

  Widget _buildDefaultAvatar() {
    return Container(
      width: radius * 2,
      height: radius * 2,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: backgroundColor ?? AppColors.primary.withValues(alpha: 0.1),
      ),
      child: Icon(Icons.person, size: radius, color: AppColors.primary),
    );
  }
}

/// 渐进式图片加载
class ProgressiveImage extends StatefulWidget {
  final String imageUrl;
  final String? thumbnailUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final BorderRadius? borderRadius;

  const ProgressiveImage({
    super.key,
    required this.imageUrl,
    this.thumbnailUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.borderRadius,
  });

  @override
  State<ProgressiveImage> createState() => _ProgressiveImageState();
}

class _ProgressiveImageState extends State<ProgressiveImage> {
  final bool _isHighResLoaded = false;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // 缩略图
        if (widget.thumbnailUrl != null)
          OptimizedNetworkImage(
            imageUrl: widget.thumbnailUrl!,
            width: widget.width,
            height: widget.height,
            fit: widget.fit,
            borderRadius: widget.borderRadius,
          ),

        // 高清图
        AnimatedOpacity(
          opacity: _isHighResLoaded ? 1.0 : 0.0,
          duration: const Duration(milliseconds: 300),
          child: OptimizedNetworkImage(
            imageUrl: widget.imageUrl,
            width: widget.width,
            height: widget.height,
            fit: widget.fit,
            borderRadius: widget.borderRadius,
            placeholder: const SizedBox.shrink(),
            errorWidget: widget.thumbnailUrl != null
                ? const SizedBox.shrink()
                : null,
          ),
        ),
      ],
    );
  }
}

/// 图片查看器
class ImageViewer extends StatelessWidget {
  final String imageUrl;
  final String? heroTag;

  const ImageViewer({super.key, required this.imageUrl, this.heroTag});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () async {
              await _shareImage();
            },
          ),
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: () async {
              await _downloadImage();
            },
          ),
        ],
      ),
      body: Center(
        child: InteractiveViewer(
          minScale: 0.5,
          maxScale: 3.0,
          child: heroTag != null
              ? Hero(
                  tag: heroTag!,
                  child: OptimizedNetworkImage(
                    imageUrl: imageUrl,
                    fit: BoxFit.contain,
                  ),
                )
              : OptimizedNetworkImage(imageUrl: imageUrl, fit: BoxFit.contain),
        ),
      ),
    );
  }

  static void show(BuildContext context, String imageUrl, {String? heroTag}) {
    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) {
          return ImageViewer(imageUrl: imageUrl, heroTag: heroTag);
        },
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(opacity: animation, child: child);
        },
      ),
    );
  }

  /// 分享图片
  Future<void> _shareImage() async {
    await ImageShareService.shareNetworkImage(
      imageUrl,
      text: '来自卡通相机的精彩作品！',
      subject: '卡通相机 - AI照片风格重绘',
    );
  }

  /// 下载图片到相册
  Future<void> _downloadImage() async {
    await ImageSaveService.saveNetworkImageToGallery(
      imageUrl,
      showLoading: true,
      showSuccess: true,
      showError: true,
    );
  }
}

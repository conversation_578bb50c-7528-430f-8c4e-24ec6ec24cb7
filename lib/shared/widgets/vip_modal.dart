import 'package:cartoon_camera/core/constants/app_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../core/services/apple_payment_service.dart';
import '../../controllers/app_controller.dart';

/// VIP会员弹窗组件
class VipModal extends StatelessWidget {
  final VoidCallback? onClose;
  final Function(String)? onPurchase;

  // 使用GetX响应式变量管理选中状态
  final RxString selectedPlan = ''.obs;

  VipModal({super.key, this.onClose, this.onPurchase});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24.r),
          topRight: Radius.circular(24.r),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 顶部装饰图片和标题
          SizedBox(height: 30.w),
          Image.asset("assets/images/purchase_vip_title.png", width: 592.w),

          // 会员套餐选项
          _buildMembershipOptions(),

          // 说明文字
          _buildDescription(),

          // 开通会员按钮
          _buildPurchaseButton(),

          // 底部安全区域
          SizedBox(height: MediaQuery.of(context).padding.bottom + 32.h),
        ],
      ),
    );
  }

  /// 构建头部区域

  /// 构建会员套餐选项
  Widget _buildMembershipOptions() {
    return GetBuilder<AppController>(
      builder: (appController) {
        final productList = appController.productInfo.value;

        // 如果产品信息还没加载完成，显示加载状态
        if (productList == null || productList.isEmpty) {
          return Container(
            margin: EdgeInsets.symmetric(horizontal: 32.w, vertical: 38.w),
            padding: EdgeInsets.only(top: 30.w),
            height: 200.w,
            child: const Center(child: CircularProgressIndicator()),
          );
        }

        final displayProducts = productList.toList();

        // 设置默认选中第一个产品
        if (displayProducts.isNotEmpty && selectedPlan.value.isEmpty) {
          selectedPlan.value = displayProducts.first.productIdentifier;
        }

        return Container(
          margin: EdgeInsets.symmetric(horizontal: 32.w, vertical: 38.w),
          padding: EdgeInsets.only(top: 30.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: displayProducts
                .asMap()
                .entries
                .map((entry) {
                  final index = entry.key;
                  final product = entry.value;

                  return [
                    if (index > 0) SizedBox(width: 38.w), // 38间距
                    Expanded(
                      child: _buildMembershipCard(
                        planId: product.productIdentifier,
                        title: product.localizedTitle,
                        price: product.discounts.isNotEmpty
                            ? product.discounts.first.localizedPrice
                            : product.localizedPrice,
                        originalPrice: product.discounts.isNotEmpty
                            ? product.localizedPrice
                            : null,
                        isRecommended: product.discounts.isNotEmpty,
                        recommendText: product.discounts.isNotEmpty
                            ? product.discounts.first.description
                            : null,
                      ),
                    ),
                  ];
                })
                .expand((widgets) => widgets)
                .toList(),
          ),
        );
      },
    );
  }

  /// 构建单个会员卡片
  Widget _buildMembershipCard({
    required String planId,
    required String title,
    required String price,
    String? originalPrice,
    bool isRecommended = false,
    String? recommendText,
  }) {
    return Obx(() {
      final isSelected = selectedPlan.value == planId;
      return Stack(
        clipBehavior: Clip.none, // 允许子组件超出边界显示
        children: [
          GestureDetector(
            onTap: () {
              selectedPlan.value = planId; // 更新选中状态
            },
            child: Container(
              padding: EdgeInsets.only(top: 38.w, bottom: 26.w),
              decoration: BoxDecoration(
                border: Border.all(
                  color: isSelected
                      ? const Color(0xFFFF6F6F) // 选中时的高亮边框
                      : const Color(0xFF424659),
                  width: 2.w,
                ),
                borderRadius: BorderRadius.circular(24.r),
                // 选中时添加背景色
                color: isSelected
                    ? const Color(0xFFFF6F6F).withValues(alpha: 0.05)
                    : Colors.white,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // 套餐名称
                  Text(
                    title,
                    style: TextStyle(
                      color: const Color(0xFF1F4B48),
                      fontSize: 26.w,
                      fontWeight: FontWeight.w500,
                    ),
                  ),

                  SizedBox(height: 8.w),

                  // 价格
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.end,

                    children: [
                      Text(
                        price,
                        style: TextStyle(
                          color: const Color(0xFFFF6F6F),
                          fontSize: 54.w,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8.w),
                  // 原价（如果有）
                  Text(
                    originalPrice ?? ' ',
                    style: TextStyle(
                      color: const Color(0xFF999999),
                      fontSize: 30.w,
                      decoration: TextDecoration.lineThrough,
                    ),
                  ),
                  // SizedBox(height: 2.w),
                ],
              ),
            ),
          ),

          // 推荐标签
          if (isRecommended && recommendText != null)
            Positioned(
              top: -25.w,
              left: 0,
              right: 0,
              child: Center(
                child: Container(
                  alignment: Alignment.center,
                  height: 50.w,
                  width: 170.w,
                  decoration: BoxDecoration(
                    color: const Color(0xFFFE692C),
                    borderRadius: BorderRadius.circular(18.w),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4.w,
                        offset: Offset(0, 2.w),
                      ),
                    ],
                  ),
                  child: Text(
                    recommendText,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 26.w,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ),
        ],
      );
    });
  }

  /// 构建说明文字
  Widget _buildDescription() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 36.w),
      child: Column(
        children: [
          // 自动续费说明
          Text(
            '* 您可在当前有效期结束前至少24小时取消会员，否则将回自动续费，向您的帐号收取续订费用，订阅款项将从您的iTunes账户中扣除。',
            style: TextStyle(
              color: const Color(0xFF999999),
              fontSize: 22.w,
              height: 30.w / 22.w,
            ),
            textAlign: TextAlign.justify,
          ),

          SizedBox(height: 14.w),

          // 协议说明
          Row(
            // mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '注：点击购买/订阅将当作您已阅读并同意',
                style: TextStyle(
                  color: const Color(0xFF999999),
                  fontSize: 22.w,
                  height: 30.w / 22.w,
                ),
              ),
              GestureDetector(
                onTap: () {
                  Get.toNamed(
                    AppConstants.routeWebView,
                    arguments: {
                      'title': '用户协议',
                      'url': AppConstants.userAgreementUrl,
                    },
                  );
                },
                child: RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: '用户协议、',
                        style: TextStyle(
                          color: const Color(0xFF999999),
                          fontSize: 22.w,
                          height: 30.w / 22.w,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      TextSpan(
                        text: '隐私协议',
                        style: TextStyle(
                          color: const Color(0xFF999999),
                          fontSize: 22.w,
                          height: 30.w / 22.w,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建开通会员按钮
  Widget _buildPurchaseButton() {
    return Container(
      margin: EdgeInsets.fromLTRB(32.w, 32.h, 32.w, 0),
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () => _handlePurchase(),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFFF6F6F),
          foregroundColor: Colors.white,
          padding: EdgeInsets.symmetric(vertical: 20.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(56.r),
          ),
          elevation: 2, // 添加阴影效果
          shadowColor: Colors.black.withValues(alpha: 0.1),
          // 按钮按下时的效果
          overlayColor: Colors.white.withValues(alpha: 0.1),
        ),
        child: Text(
          '开通会员',
          style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.w500),
        ),
      ),
    );
  }

  /// 处理购买会员
  void _handlePurchase() async {
    try {
      // 显示加载对话框
      Get.dialog(
        const Center(child: CircularProgressIndicator()),
        barrierDismissible: false,
      );

      // 获取苹果支付服务
      final paymentService = Get.find<ApplePaymentService>();

      // 发起购买
      final success = await paymentService.purchaseMembership(
        selectedPlan.value,
      );

      // 关闭加载对话框
      Get.back();

      if (success) {
        // 购买成功，调用原有的回调
        await onPurchase?.call(selectedPlan.value);

        // 关闭弹窗
        Get.back();

        // 显示成功提示
        Get.snackbar(
          '购买成功',
          '恭喜您成功开通会员！',
          snackPosition: SnackPosition.TOP,
          backgroundColor: const Color(0xFF4CAF50),
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );
      } else {
        // 显示错误提示
        Get.snackbar(
          '购买失败',
          '购买过程中出现错误',
          snackPosition: SnackPosition.TOP,
          backgroundColor: const Color(0xFFFF5722),
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );
      }
    } catch (e) {
      // 关闭加载对话框
      if (Get.isDialogOpen == true) {
        Get.back();
      }
      debugPrint('购买失败: $e');

      // 检查是否是用户取消
      if (e.toString().contains('userCancelled')) {
        Get.snackbar(
          '购买取消',
          '您已取消购买',
          snackPosition: SnackPosition.TOP,
          backgroundColor: const Color(0xFF9E9E9E),
          colorText: Colors.white,
          duration: const Duration(seconds: 2),
        );
      } else {
        Get.snackbar(
          '购买失败',
          '购买过程中出现错误',
          snackPosition: SnackPosition.TOP,
          backgroundColor: const Color(0xFFFF5722),
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );
      }
    }
  }

  /// 显示VIP弹窗
  static Future<void> show({
    VoidCallback? onClose,
    Function(String)? onPurchase,
  }) {
    return Get.bottomSheet(
      VipModal(onClose: onClose, onPurchase: onPurchase),
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.black.withValues(alpha: 0.5),
    );
  }
}

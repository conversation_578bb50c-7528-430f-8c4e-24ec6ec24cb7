import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_constants.dart';

/// 自定义对话框工具类
class CustomDialogs {
  /// 显示确认对话框
  static Future<bool?> showConfirmDialog({
    required String title,
    required String content,
    String confirmText = '确定',
    String cancelText = '取消',
    Color? confirmColor,
    bool barrierDismissible = true,
  }) {
    return Get.dialog<bool>(
      AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: Text(
              cancelText,
              style: TextStyle(color: AppColors.textSecondary),
            ),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: Text(
              confirmText,
              style: TextStyle(color: confirmColor ?? AppColors.primary),
            ),
          ),
        ],
      ),
      barrierDismissible: barrierDismissible,
    );
  }

  /// 显示信息对话框
  static Future<void> showInfoDialog({
    required String title,
    required String content,
    String buttonText = '确定',
    VoidCallback? onConfirm,
  }) {
    return Get.dialog(
      AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
              onConfirm?.call();
            },
            child: Text(buttonText),
          ),
        ],
      ),
    );
  }

  /// 显示错误对话框
  static Future<void> showErrorDialog({
    String title = '错误',
    required String content,
    String buttonText = '确定',
    VoidCallback? onConfirm,
  }) {
    return showInfoDialog(
      title: title,
      content: content,
      buttonText: buttonText,
      onConfirm: onConfirm,
    );
  }

  /// 显示成功对话框
  static Future<void> showSuccessDialog({
    String title = '成功',
    required String content,
    String buttonText = '确定',
    VoidCallback? onConfirm,
  }) {
    return showInfoDialog(
      title: title,
      content: content,
      buttonText: buttonText,
      onConfirm: onConfirm,
    );
  }

  /// 显示输入对话框
  static Future<String?> showInputDialog({
    required String title,
    String? hint,
    String? initialValue,
    String confirmText = '确定',
    String cancelText = '取消',
    TextInputType keyboardType = TextInputType.text,
    int? maxLength,
    String? Function(String?)? validator,
  }) {
    final TextEditingController controller = TextEditingController(
      text: initialValue,
    );
    final GlobalKey<FormState> formKey = GlobalKey<FormState>();

    return Get.dialog<String>(
      AlertDialog(
        title: Text(title),
        content: Form(
          key: formKey,
          child: TextFormField(
            controller: controller,
            decoration: InputDecoration(
              hintText: hint,
              counterText: maxLength != null ? null : '',
            ),
            keyboardType: keyboardType,
            maxLength: maxLength,
            validator: validator,
            autofocus: true,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              cancelText,
              style: TextStyle(color: AppColors.textSecondary),
            ),
          ),
          TextButton(
            onPressed: () {
              if (formKey.currentState?.validate() ?? true) {
                Get.back(result: controller.text);
              }
            },
            child: Text(confirmText),
          ),
        ],
      ),
    );
  }

  /// 显示选择对话框
  static Future<T?> showChoiceDialog<T>({
    required String title,
    required List<ChoiceItem<T>> choices,
    T? selectedValue,
    String cancelText = '取消',
  }) {
    return Get.dialog<T>(
      AlertDialog(
        title: Text(title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: choices.map((choice) {
            return RadioListTile<T>(
              title: Text(choice.title),
              subtitle: choice.subtitle != null ? Text(choice.subtitle!) : null,
              value: choice.value,
              groupValue: selectedValue,
              onChanged: (value) => Get.back(result: value),
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              cancelText,
              style: TextStyle(color: AppColors.textSecondary),
            ),
          ),
        ],
      ),
    );
  }

  /// 显示加载对话框
  static void showLoadingDialog({
    String? message,
    bool barrierDismissible = false,
  }) {
    Get.dialog(
      AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(
              strokeWidth: 2.w,
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
            ),
            SizedBox(width: AppConstants.defaultPadding.w),
            Expanded(child: Text(message ?? '加载中...')),
          ],
        ),
      ),
      barrierDismissible: barrierDismissible,
    );
  }

  /// 隐藏加载对话框
  static void hideLoadingDialog() {
    if (Get.isDialogOpen ?? false) {
      Get.back();
    }
  }

  /// 显示底部选择器
  static Future<T?> showBottomPicker<T>({
    required String title,
    required List<PickerItem<T>> items,
    String cancelText = '取消',
  }) {
    return Get.bottomSheet<T>(
      Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题栏
            Container(
              padding: EdgeInsets.all(AppConstants.defaultPadding.w),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: AppColors.border, width: 1.w),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  TextButton(
                    onPressed: () => Get.back(),
                    child: Text(cancelText),
                  ),
                ],
              ),
            ),

            // 选项列表
            ...items.map((item) {
              return ListTile(
                leading: item.icon != null ? Icon(item.icon) : null,
                title: Text(item.title),
                subtitle: item.subtitle != null ? Text(item.subtitle!) : null,
                onTap: () => Get.back(result: item.value),
              );
            }),

            SizedBox(height: MediaQuery.of(Get.context!).padding.bottom),
          ],
        ),
      ),
    );
  }
}

/// 选择项数据类
class ChoiceItem<T> {
  final String title;
  final String? subtitle;
  final T value;

  const ChoiceItem({required this.title, this.subtitle, required this.value});
}

/// 选择器项数据类
class PickerItem<T> {
  final String title;
  final String? subtitle;
  final IconData? icon;
  final T value;

  const PickerItem({
    required this.title,
    this.subtitle,
    this.icon,
    required this.value,
  });
}

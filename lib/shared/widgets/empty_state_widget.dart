import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_constants.dart';

/// 空状态组件
class EmptyStateWidget extends StatelessWidget {
  final IconData icon;
  final String title;
  final String? subtitle;
  final String? buttonText;
  final VoidCallback? onButtonPressed;
  final Color? iconColor;

  const EmptyStateWidget({
    super.key,
    required this.icon,
    required this.title,
    this.subtitle,
    this.buttonText,
    this.onButtonPressed,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(AppConstants.largePadding.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 图标
            Container(
              width: 120.w,
              height: 120.h,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: (iconColor ?? AppColors.textSecondary).withValues(alpha: 0.1),
              ),
              child: Icon(
                icon,
                size: 60.sp,
                color: iconColor ?? AppColors.textSecondary,
              ),
            ),
            
            SizedBox(height: AppConstants.largePadding.h),
            
            // 标题
            Text(
              title,
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            
            if (subtitle != null) ...[
              SizedBox(height: AppConstants.smallPadding.h),
              Text(
                subtitle!,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.textSecondary,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
            ],
            
            if (buttonText != null && onButtonPressed != null) ...[
              SizedBox(height: AppConstants.largePadding.h),
              ElevatedButton(
                onPressed: onButtonPressed,
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(
                    horizontal: AppConstants.largePadding.w,
                    vertical: AppConstants.defaultPadding.h,
                  ),
                ),
                child: Text(buttonText!),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// 错误状态组件
class ErrorStateWidget extends StatelessWidget {
  final String title;
  final String? subtitle;
  final String? buttonText;
  final VoidCallback? onButtonPressed;
  final IconData? icon;

  const ErrorStateWidget({
    super.key,
    required this.title,
    this.subtitle,
    this.buttonText,
    this.onButtonPressed,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: icon ?? Icons.error_outline,
      title: title,
      subtitle: subtitle,
      buttonText: buttonText ?? '重试',
      onButtonPressed: onButtonPressed,
      iconColor: AppColors.error,
    );
  }
}

/// 网络错误组件
class NetworkErrorWidget extends StatelessWidget {
  final VoidCallback? onRetry;

  const NetworkErrorWidget({
    super.key,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return ErrorStateWidget(
      icon: Icons.wifi_off,
      title: '网络连接失败',
      subtitle: '请检查网络设置后重试',
      buttonText: '重新连接',
      onButtonPressed: onRetry,
    );
  }
}

/// 无权限组件
class NoPermissionWidget extends StatelessWidget {
  final String title;
  final String subtitle;
  final VoidCallback? onRequestPermission;

  const NoPermissionWidget({
    super.key,
    required this.title,
    required this.subtitle,
    this.onRequestPermission,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.lock_outline,
      title: title,
      subtitle: subtitle,
      buttonText: '授权',
      onButtonPressed: onRequestPermission,
      iconColor: AppColors.warning,
    );
  }
}

/// 搜索无结果组件
class NoSearchResultWidget extends StatelessWidget {
  final String searchQuery;
  final VoidCallback? onClearSearch;

  const NoSearchResultWidget({
    super.key,
    required this.searchQuery,
    this.onClearSearch,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.search_off,
      title: '未找到相关内容',
      subtitle: '没有找到与"$searchQuery"相关的内容\n请尝试其他关键词',
      buttonText: '清除搜索',
      onButtonPressed: onClearSearch,
    );
  }
}

/// 数据为空组件
class NoDataWidget extends StatelessWidget {
  final String title;
  final String? subtitle;
  final VoidCallback? onRefresh;

  const NoDataWidget({
    super.key,
    required this.title,
    this.subtitle,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.inbox_outlined,
      title: title,
      subtitle: subtitle ?? '暂无数据',
      buttonText: onRefresh != null ? '刷新' : null,
      onButtonPressed: onRefresh,
    );
  }
}

/// 维护中组件
class MaintenanceWidget extends StatelessWidget {
  final String? message;

  const MaintenanceWidget({
    super.key,
    this.message,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.build_outlined,
      title: '系统维护中',
      subtitle: message ?? '系统正在维护升级，请稍后再试',
      iconColor: AppColors.warning,
    );
  }
}

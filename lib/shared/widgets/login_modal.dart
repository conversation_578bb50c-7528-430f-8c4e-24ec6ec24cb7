import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../core/services/auth_service.dart';
import '../../services/api_service.dart';

/// 登录弹窗组件
/// 底部弹出的登录弹窗，支持苹果登录
class LoginModal extends StatelessWidget {
  final VoidCallback? onClose;
  final Function(String)? onLoginSuccess;

  const LoginModal({super.key, this.onClose, this.onLoginSuccess});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,

      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(32.w),
          topRight: Radius.circular(32.w),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 头部装饰区域
          SizedBox(height: 140.w),
          Image.asset(
            'assets/images/login_slogan.png',
            width: 583.w,
            height: 280.w,
            fit: BoxFit.contain,
          ),
          SizedBox(height: 84.w),

          // 登录按钮区域
          _buildAppleLoginButton(),

          // 底部安全区域
          SizedBox(height: MediaQuery.of(context).padding.bottom + 32.h),
        ],
      ),
    );
  }

  /// 构建苹果登录按钮
  Widget _buildAppleLoginButton() {
    return GestureDetector(
      onTap: _handleAppleLogin,
      child: Container(
        width: 600.w,
        height: 92.w,
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(46.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8.r,
              offset: Offset(0, 2.h),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 苹果图标
            Icon(Icons.apple, size: 38.w, color: Colors.white),

            SizedBox(width: 24.w),

            // 按钮文字
            Text(
              '通过苹果登录',
              style: TextStyle(
                color: Colors.white,
                fontSize: 41.w,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 处理苹果登录
  void _handleAppleLogin() async {
    try {
      // 显示加载状态
      Get.dialog(
        const Center(child: CircularProgressIndicator()),
        barrierDismissible: false,
      );

      final apiService = Get.find<ApiService>();
      final authService = Get.find<AuthService>();

      // 执行登录
      final accessToken = await apiService.appleLogin();

      // 保存token
      await authService.saveToken(accessToken);

      // 关闭加载弹窗
      Get.back();

      // 关闭登录弹窗
      Get.back();

      // 调用成功回调
      if (onLoginSuccess != null) {
        onLoginSuccess!(accessToken);
      }

      // 显示成功提示
      Get.snackbar(
        '登录成功',
        '欢迎使用卡通相机',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      // 关闭加载弹窗
      if (Get.isDialogOpen == true) {
        Get.back();
      }

      // 显示错误提示
      Get.snackbar(
        '登录失败',
        e.toString(),
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 显示登录弹窗
  static Future<void> show({
    VoidCallback? onClose,
    Function(String)? onLoginSuccess,
  }) {
    return Get.bottomSheet(
      LoginModal(onClose: onClose, onLoginSuccess: onLoginSuccess),
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.black.withValues(alpha: 0.5),
    );
  }
}

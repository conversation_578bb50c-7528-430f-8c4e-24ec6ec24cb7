import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'app/app_binding.dart';
import 'core/errors/error_handler.dart';
import 'core/config/image_cache_config.dart';
import 'app/app.dart';

/// 应用入口
void main() async {
  // 确保Flutter绑定初始化
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化全局错误处理
  ErrorHandler.initialize();

  // 初始化图片缓存配置
  ImageCacheConfig.initialize();

  // 设置系统UI样式
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  // 设置首选方向为竖屏
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // 初始化应用依赖
  await AppInitializer.initialize();

  // 启动应用
  runApp(const MyApp());
}

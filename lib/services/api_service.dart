import 'package:cartoon_camera/core/services/apple_sign_in_service.dart';
import 'package:cartoon_camera/core/services/auth_service.dart';
import 'package:cartoon_camera/shared/models/template_response_model.dart';
import 'package:get/get.dart';
import 'package:dio/dio.dart' as dio;
import 'package:logger/logger.dart';
import 'package:flutter/foundation.dart';

/// API服务类 - 负责所有网络请求的具体实现
/// 自己管理Dio实例，支持独立配置baseUrl和拦截器
class ApiService extends GetxService {
  late final dio.Dio _dio;
  final Logger _logger = Logger();
  static const String baseUrl = "https://api.cloudfare.com.cn";

  /// 获取dio实例
  /// 构造函数 - 可配置baseUrl和拦截器
  ApiService({
    String? baseUrl,
    int connectTimeout = 30000,
    int receiveTimeout = 30000,
    Map<String, dynamic>? defaultHeaders,
    List<dio.Interceptor>? interceptors,
  }) {
    _dio = dio.Dio(
      dio.BaseOptions(
        baseUrl: baseUrl ?? ApiService.baseUrl,
        connectTimeout: Duration(milliseconds: connectTimeout),
        receiveTimeout: Duration(milliseconds: receiveTimeout),
        headers:
            defaultHeaders ??
            {'Content-Type': 'application/json', 'Accept': 'application/json'},
        // 配置状态码验证：200-399 都认为是成功
        validateStatus: (status) {
          return status != null && status >= 200 && status < 400;
        },
      ),
    );

    // 设置默认拦截器
    _setupDefaultInterceptors();

    // 添加自定义拦截器
    if (interceptors != null) {
      _dio.interceptors.addAll(interceptors);
    }
  }

  /// 设置默认拦截器
  void _setupDefaultInterceptors() {
    // 添加 DevTools 网络监控拦截器（仅在调试模式下）
    if (kDebugMode) {
      _dio.interceptors.add(
        dio.LogInterceptor(
          requestBody: true,
          responseBody: true,
          requestHeader: true,
          responseHeader: true,
          error: true,
        ),
      );
    }

    _dio.interceptors.add(
      dio.InterceptorsWrapper(
        onRequest: (options, handler) {
          try {
            final authService = Get.find<AuthService>();
            // header 增加auth token
            final token = authService.getTokenFromStorage();
            if (token.isNotEmpty) {
              options.headers['Authorization'] = 'Bearer $token';
            }
          } catch (e) {
            // AuthService 可能还未初始化，忽略错误
            _logger.w('获取token失败: $e');
          }
          _logger.d('请求: ${options.method} ${options.path}');
          _logger.d('请求头: ${options.headers}');
          _logger.d('请求数据: ${options.data}');
          handler.next(options);
        },
        onResponse: (response, handler) {
          _logger.d(
            '响应: ${response.statusCode} ${response.requestOptions.path}',
          );
          _logger.d('响应数据: ${response.data}');

          // 拦截器只处理网络层错误，业务逻辑在 _handleResponse 中处理
          // 2xx 和 3xx 状态码都认为是成功
          if (response.statusCode! >= 200 && response.statusCode! < 400) {
            handler.next(response);
          } else {
            // 4xx 和 5xx 状态码才是错误
            Get.snackbar(
              '错误',
              '网络错误: ${response.statusCode} ${response.statusMessage}',
            );
            handler.reject(
              dio.DioException(
                requestOptions: response.requestOptions,
                response: response,
                type: dio.DioExceptionType.badResponse,
                message: '网络错误: ${response.statusCode}',
              ),
            );
          }
        },
        onError: (error, handler) {
          _logger.e('网络错误: ${error.message}');
          _logger.e('错误详情: ${error.response?.data}');
          handler.next(error);
        },
      ),
    );
  }

  /// 添加拦截器
  void addInterceptor(dio.Interceptor interceptor) {
    _dio.interceptors.add(interceptor);
  }

  /// 移除拦截器
  void removeInterceptor(dio.Interceptor interceptor) {
    _dio.interceptors.remove(interceptor);
  }

  /// 更新baseUrl
  void updateBaseUrl(String baseUrl) {
    _dio.options.baseUrl = baseUrl;
  }

  /// 更新默认请求头
  void updateHeaders(Map<String, dynamic> headers) {
    _dio.options.headers.addAll(headers);
  }

  /// 统一的响应处理方法
  /// 使用ApiResponse模型处理业务逻辑层的响应解包和错误处理
  T _handleResponse<T>(dio.Response response, T Function(dynamic)? fromJson) {
    if (response.data == null) {
      throw Exception('响应数据为空');
    }

    final responseData = response.data;

    // 使用ApiResponse模型解析响应
    if (responseData is Map<String, dynamic>) {
      final apiResponse = ApiResponse<dynamic>.fromJson(
        responseData,
        (json) => json, // 临时转换函数，实际数据在后面处理
      );

      if (apiResponse.success && apiResponse.code == 0) {
        // 成功响应，返回解包后的数据
        final data = apiResponse.data;
        if (fromJson != null && data != null) {
          return fromJson(data);
        }
        return data as T;
      } else {
        // 业务逻辑错误
        Get.snackbar('错误', apiResponse.message);
        throw Exception(apiResponse.message);
      }
    } else {
      // 直接返回数据（用于某些特殊接口）
      if (fromJson != null) {
        return fromJson(responseData);
      }
      return responseData as T;
    }
  }

  /// 获取模板风格列表
  /// GET /api/cartoon/templates
  Future<TemplateData> getTemplates() async {
    try {
      final response = await _dio.get('/api/cartoon/templates');
      return _handleResponse<TemplateData>(
        response,
        (data) => TemplateData.fromJson(data),
      );
    } catch (e) {
      if (e is dio.DioException) {
        throw _handleDioError(e);
      }
      rethrow;
    }
  }

  // 获取oss配置信息
  Future<OSSConfigData> getOssConfig() async {
    try {
      final response = await _dio.get('/api/cartoon/oss/config');
      return _handleResponse<OSSConfigData>(
        response,
        (data) => OSSConfigData.fromJson(data),
      );
    } catch (e) {
      if (e is dio.DioException) {
        throw _handleDioError(e);
      }
      rethrow;
    }
  }

  /// 获取STS 临时凭证
  /// GET /api/cartoon/oss/sts
  Future<STSInfoData> getStsInfo() async {
    try {
      final response = await _dio.get('/api/cartoon/oss/sts');
      return _handleResponse<STSInfoData>(
        response,
        (data) => STSInfoData.fromJson(data),
      );
    } catch (e) {
      if (e is dio.DioException) {
        throw _handleDioError(e);
      }
      rethrow;
    }
  }

  /// 生成卡通图
  /// POST /api/cartoon/image/transform
  Future<String> generateCartoon(ImageGenerationRequest request) async {
    _logger.d('开始调用生成API${request.toJson()}');
    try {
      final response = await _dio.post(
        '/api/cartoon/image/transform',
        data: request,
      );
      return _handleResponse<String>(response, (data) => data as String);
    } catch (e) {
      if (e is dio.DioException) {
        Get.snackbar('登录失败', '登录失败,请稍后再试');
      }
      rethrow;
    }
  }

  /// 查询使用量
  /// POST /api/cartoon/transformed/count
  Future<num> getUsageCount(Map<String, int> request) async {
    try {
      final response = await _dio.post(
        '/api/cartoon/transformed/count',
        data: request,
      );
      return _handleResponse<num>(response, (data) => data as num);
    } catch (e) {
      if (e is dio.DioException) {
        throw _handleDioError(e);
      }
      rethrow;
    }
  }
  // 获取内购产品信息

  Future<List<ProductInfo>> getProductInfo() async {
    try {
      final response = await _dio.get('/api/cartoon/products');
      return _handleResponse<List<ProductInfo>>(
        response,
        (data) =>
            (data as List).map((item) => ProductInfo.fromJson(item)).toList(),
      );
    } catch (e) {
      if (e is dio.DioException) {
        throw _handleDioError(e); //
      }
      rethrow;
    }
  }

  // 获取首次评分引导数据
  Future<RatingGuideData> getFirstRatingGuide() async {
    try {
      final response = await _dio.get('/api/cartoon/score/first');
      return _handleResponse<RatingGuideData>(
        response,
        (data) => RatingGuideData.fromJson(data),
      );
    } catch (e) {
      if (e is dio.DioException) {
        throw _handleDioError(e);
      }
      rethrow;
    }
  }

  // 获取次日评分引导数据
  Future<RatingGuideData> getSecondRatingGuide() async {
    try {
      final response = await _dio.get('/api/cartoon/score/next');
      return _handleResponse<RatingGuideData>(
        response,
        (data) => RatingGuideData.fromJson(data),
      );
    } catch (e) {
      if (e is dio.DioException) {
        throw _handleDioError(e);
      }
      rethrow;
    }
  }

  /// 苹果登录
  /// POST /api/auth/login/apple
  Future<String> appleLogin() async {
    try {
      _logger.i('Apple 登录开始');
      final appleSignInService = AppleSignInService();
      final appleLoginInfo = await appleSignInService.signIn();

      // 提取 Apple 登录信息的详细字段
      final loginData = {
        'user': appleLoginInfo.userIdentifier,
        'email': appleLoginInfo.email,
        'givenName': appleLoginInfo.givenName,
        'familyName': appleLoginInfo.familyName,
        'identityToken': appleLoginInfo.identityToken,
        'authorizationCode': appleLoginInfo.authorizationCode,
        'state': appleLoginInfo.state,
        'nonce': appleSignInService.nonce,
      };

      _logger.i('=== 发送到服务器的数据 ===');
      _logger.i('$loginData');

      final response = await _dio.post(
        '/api/auth/login/apple',
        data: loginData,
      );
      return _handleResponse<String>(
        response,
        (data) => LoginData.fromJson(data).accessToken,
      );
    } catch (e) {
      if (e is dio.DioException) {
        throw _handleDioError(e);
      }
      rethrow;
    }
  }

  /// 通用请求方法
  Future<Map<String, dynamic>> request(
    String method,
    String url,
    Map<String, dynamic> data,
  ) async {
    try {
      late final dio.Response response;

      switch (method.toUpperCase()) {
        case 'GET':
          response = await _dio.get(url, queryParameters: data);
          break;
        case 'POST':
          response = await _dio.post(url, data: data);
          break;
        case 'PUT':
          response = await _dio.put(url, data: data);
          break;
        case 'DELETE':
          response = await _dio.delete(url, data: data);
          break;
        default:
          throw Exception('不支持的请求方法: $method');
      }

      return _handleResponse<Map<String, dynamic>>(
        response,
        (data) => data as Map<String, dynamic>,
      );
    } catch (e) {
      if (e is dio.DioException) {
        throw _handleDioError(e);
      }
      rethrow;
    }
  }

  /// 处理Dio错误
  Exception _handleDioError(dio.DioException error) {
    switch (error.type) {
      case dio.DioExceptionType.connectionTimeout:
      case dio.DioExceptionType.sendTimeout:
      case dio.DioExceptionType.receiveTimeout:
        return Exception('网络连接超时，请检查网络设置');
      case dio.DioExceptionType.badResponse:
        return Exception('服务器错误: ${error.response?.statusCode}');
      case dio.DioExceptionType.cancel:
        return Exception('请求已取消');
      case dio.DioExceptionType.unknown:
        return Exception('网络连接失败: ${error.message}');
      default:
        return Exception('未知错误: ${error.message}');
    }
  }
}

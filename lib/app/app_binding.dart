import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:logger/logger.dart';
import '../services/api_service.dart';
import '../core/services/auth_service.dart';
import '../core/services/oss_service.dart';
import '../core/services/umeng_service.dart';
import '../core/services/apple_payment_service.dart';
import '../controllers/app_controller.dart';

/// 应用全局依赖绑定
/// 管理所有核心服务的依赖注入
class AppBinding extends Bindings {
  final Logger _logger = Logger();

  @override
  void dependencies() {
    // 网络层服务
    Get.put<ApiService>(
      ApiService(
        baseUrl: 'https://api.cloudfare.com.cn',
        connectTimeout: 30000,
        receiveTimeout: 30000,
      ),
      permanent: true,
    );

    // 核心业务服务 (按依赖顺序注册)
    // 1. 基础服务 (无依赖)
    Get.put<AuthService>(AuthService(), permanent: true);
    Get.put<UmengService>(UmengService(), permanent: true);

    // 2. 依赖基础服务的服务
    Get.put<OssService>(OssService(), permanent: true);

    // 工具服务 (需要先注册，因为其他服务依赖它们)
    Get.put<ApplePaymentService>(ApplePaymentService(), permanent: true);

    // 功能服务 (依赖工具服务)
    // GenerationService 已移除，生成逻辑直接在页面 Controller 中处理

    // 应用控制器
    Get.put<AppController>(AppController(), permanent: true);

    // 初始化需要异步操作的服务
    _initializeAsyncServices();
  }

  /// 初始化需要异步操作的服务
  void _initializeAsyncServices() {
    // 在下一个事件循环中初始化友盟服务
    Future.microtask(() async {
      try {
        await Get.find<UmengService>().initialize();
      } catch (e) {
        // 友盟初始化失败不应该阻止应用启动
        _logger.e('友盟服务初始化失败: $e');
      }
    });
  }
}

/// 初始化应用依赖
/// 在应用启动时调用
class AppInitializer {
  static Future<void> initialize() async {
    // 初始化 GetStorage
    await GetStorage.init();
  }
}

import 'package:get/get.dart';
import '../features/home/<USER>/home_controller.dart';
import '../features/list/providers/list_controller.dart';
import '../features/detail/providers/detail_controller.dart';
import '../features/generation/providers/generation_controller.dart';
import '../features/result/providers/result_controller.dart';
import '../features/profile/providers/profile_controller.dart';

/// 首页绑定
class HomeBinding extends Bindings {
  @override
  void dependencies() {
    Get.put<HomeController>(HomeController());
  }
}

/// 风格列表绑定
class ListBinding extends Bindings {
  @override
  void dependencies() {
    Get.put<ListController>(ListController());
  }
}

/// 风格详情绑定
class DetailBinding extends Bindings {
  @override
  void dependencies() {
    Get.put<DetailController>(DetailController());
  }
}

/// 图片生成绑定
class GenerationBinding extends Bindings {
  @override
  void dependencies() {
    Get.put<GenerationController>(GenerationController());
  }
}

/// 生成结果绑定
class ResultBinding extends Bindings {
  @override
  void dependencies() {
    Get.put<ResultController>(ResultController());
  }
}

/// 个人中心绑定
class ProfileBinding extends Bindings {
  @override
  void dependencies() {
    Get.put<ProfileController>(ProfileController());
  }
}

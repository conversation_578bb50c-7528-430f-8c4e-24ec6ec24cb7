import 'package:get/get.dart';
import '../features/home/<USER>/home_page.dart';
import '../features/list/pages/list_page.dart';
import '../features/detail/pages/detail_page.dart';
import '../features/generation/pages/generation_page.dart';
import '../features/result/pages/result_page.dart';
import '../features/profile/pages/profile_page.dart';
import '../shared/pages/webview_page.dart';
import 'pages_binding.dart';

/// 应用路由配置
class AppRoutes {
  /// 路由列表
  static final routes = [
    GetPage(name: '/', page: () => const HomePage(), binding: HomeBinding()),
    GetPage(
      name: '/list',
      page: () => const ListPage(),
      binding: ListBinding(),
    ),
    GetPage(
      name: '/detail',
      page: () => const DetailPage(),
      binding: DetailBinding(),
    ),
    GetPage(
      name: '/generation',
      page: () => const GenerationPage(),
      binding: GenerationBinding(),
    ),
    GetPage(
      name: '/result',
      page: () => const ResultPage(),
      binding: ResultBinding(),
    ),
    GetPage(
      name: '/profile',
      page: () => const ProfilePage(),
      binding: ProfileBinding(),
    ),
    GetPage(name: '/webview', page: () => const WebViewPage()),
  ];
}

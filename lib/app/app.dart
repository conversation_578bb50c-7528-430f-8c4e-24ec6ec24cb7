import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../core/constants/app_constants.dart';
import 'app_binding.dart';
import 'routes.dart';
import 'themes.dart';

/// 主应用配置
class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(750, 1624), // 设计稿尺寸
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return GetMaterialApp(
          title: AppConstants.appName,
          debugShowCheckedModeBanner: false,

          // 依赖注入配置
          initialBinding: AppBinding(),

          // 主题配置
          theme: AppTheme.lightTheme,
          // darkTheme: AppTheme.darkTheme,
          // themeMode: ThemeMode.system,

          // 路由配置
          initialRoute: '/',
          getPages: AppRoutes.routes,

          // 国际化配置
          locale: const Locale('zh', 'CN'),
          fallbackLocale: const Locale('en', 'US'),

          // 默认过渡动画
          defaultTransition: Transition.cupertino,
          transitionDuration: AppConstants.animationDuration,

          // 错误处理
          unknownRoute: GetPage(
            name: '/notfound',
            page: () => const Scaffold(body: Center(child: Text('页面未找到'))),
          ),
        );
      },
    );
  }
}

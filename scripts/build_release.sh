#!/bin/bash

# 卡通相机 iOS 发布构建脚本
# 此脚本用于生成App Store发布版本

set -e

echo "🚀 开始构建卡通相机 iOS 发布版本..."

# 检查是否在项目根目录
if [ ! -f "pubspec.yaml" ]; then
    echo "❌ 错误：请在Flutter项目根目录运行此脚本"
    exit 1
fi

# 检查Flutter环境
if ! command -v flutter &> /dev/null; then
    echo "❌ 错误：Flutter未安装或未添加到PATH"
    exit 1
fi

# 检查Xcode
if ! command -v xcodebuild &> /dev/null; then
    echo "❌ 错误：Xcode未安装或命令行工具未配置"
    exit 1
fi

echo "📦 清理之前的构建..."
flutter clean

echo "📥 获取依赖..."
flutter pub get

echo "🔧 生成代码..."
flutter packages pub run build_runner build --delete-conflicting-outputs

echo "📱 安装iOS依赖..."
cd ios && pod install && cd ..

echo "🏗️ 构建发布版本..."
flutter build ipa --release \
    --obfuscate \
    --split-debug-info=build/app/outputs/symbols \
    --build-name=1.0.0 \
    --build-number=1

echo "✅ 构建完成！"
echo ""
echo "📍 构建文件位置："
echo "   IPA文件: build/ios/ipa/cartoon_camera.ipa"
echo "   Archive: build/ios/archive/"
echo ""
echo "📋 下一步操作："
echo "1. 在Xcode中验证签名配置"
echo "2. 使用Apple Transporter上传IPA文件"
echo "3. 或在Xcode中打开Archive进行分发"
echo ""
echo "🔗 有用的命令："
echo "   打开Archive: open build/ios/archive/"
echo "   打开IPA目录: open build/ios/ipa/"

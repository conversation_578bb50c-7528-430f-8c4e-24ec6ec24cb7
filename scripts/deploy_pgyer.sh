#!/bin/bash

# 卡通相机 蒲公英分发脚本
# 此脚本用于将应用打包并上传到蒲公英平台进行内测分发

set -e

echo "🚀 卡通相机 蒲公英分发工具"
echo "=========================="

# 检查是否在项目根目录
if [ ! -f "pubspec.yaml" ]; then
    echo "❌ 错误：请在Flutter项目根目录运行此脚本"
    exit 1
fi

# 检查Flutter环境
if ! command -v flutter &> /dev/null; then
    echo "❌ 错误：Flutter未安装或未添加到PATH"
    exit 1
fi

# 检查jq工具（用于解析JSON响应）
if ! command -v jq &> /dev/null; then
    echo "⚠️  警告：未安装jq工具，建议安装以获得更好的体验"
    echo "安装命令：brew install jq"
fi

# 获取蒲公英API Key
if [ -z "$PGYER_API_KEY" ]; then
    echo "🔑 请输入蒲公英API Key："
    echo "（可在蒲公英官网 https://www.pgyer.com/account/api 获取）"
    read -p "API Key: " PGYER_API_KEY
    
    if [ -z "$PGYER_API_KEY" ]; then
        echo "❌ 错误：API Key不能为空"
        exit 1
    fi
fi

# 获取更新描述
read -p "📝 请输入更新描述（可选）: " UPDATE_DESCRIPTION
if [ -z "$UPDATE_DESCRIPTION" ]; then
    UPDATE_DESCRIPTION="卡通相机测试版本 - $(date '+%Y-%m-%d %H:%M')"
fi

echo ""
echo "📦 开始构建iOS测试版本..."

# 清理之前的构建
flutter clean

# 获取依赖
flutter pub get

# 生成代码
flutter packages pub run build_runner build --delete-conflicting-outputs

# 安装iOS依赖
cd ios && pod install && cd ..

# 构建Ad-Hoc版本（用于内测分发）
echo "🏗️ 构建Ad-Hoc版本..."
flutter build ipa --release \
    --export-method ad-hoc \
    --build-name=1.0.0 \
    --build-number=$(date +%s)

# 检查IPA文件是否生成成功
IPA_PATH=$(find build/ios/ipa -name "*.ipa" | head -1)
if [ ! -f "$IPA_PATH" ]; then
    echo "❌ 错误：IPA文件生成失败"
    exit 1
fi

echo "✅ IPA文件生成成功: $IPA_PATH"

# 获取文件大小
IPA_SIZE=$(du -h "$IPA_PATH" | cut -f1)
echo "📦 文件大小: $IPA_SIZE"

echo ""
echo "📤 开始上传到蒲公英..."

# 上传到蒲公英
UPLOAD_RESPONSE=$(curl -s \
    -F "file=@$IPA_PATH" \
    -F "_api_key=$PGYER_API_KEY" \
    -F "buildInstallType=2" \
    -F "buildPassword=123456" \
    -F "buildUpdateDescription=$UPDATE_DESCRIPTION" \
    https://www.pgyer.com/apiv2/app/upload)

# 检查上传结果
if command -v jq &> /dev/null; then
    # 使用jq解析JSON响应
    CODE=$(echo "$UPLOAD_RESPONSE" | jq -r '.code')
    if [ "$CODE" = "0" ]; then
        BUILD_KEY=$(echo "$UPLOAD_RESPONSE" | jq -r '.data.buildKey')
        BUILD_VERSION=$(echo "$UPLOAD_RESPONSE" | jq -r '.data.buildVersion')
        BUILD_SHORT_URL=$(echo "$UPLOAD_RESPONSE" | jq -r '.data.buildShortcutUrl')
        
        echo ""
        echo "🎉 上传成功！"
        echo "📱 应用版本: $BUILD_VERSION"
        echo "🔗 下载链接: https://www.pgyer.com/$BUILD_SHORT_URL"
        echo "🔒 安装密码: 123456"
        echo ""
        echo "📋 分享信息："
        echo "应用名称: 卡通相机"
        echo "版本号: $BUILD_VERSION"
        echo "下载地址: https://www.pgyer.com/$BUILD_SHORT_URL"
        echo "安装密码: 123456"
        echo "更新说明: $UPDATE_DESCRIPTION"
        
        # 生成二维码链接
        echo ""
        echo "📱 扫码下载: https://www.pgyer.com/$BUILD_SHORT_URL"
        
    else
        MESSAGE=$(echo "$UPLOAD_RESPONSE" | jq -r '.message')
        echo "❌ 上传失败: $MESSAGE"
        exit 1
    fi
else
    # 简单检查响应
    if [[ "$UPLOAD_RESPONSE" == *"\"code\":0"* ]]; then
        echo "🎉 上传成功！"
        echo "📱 请登录蒲公英官网查看详细信息"
        echo "🔗 蒲公英官网: https://www.pgyer.com/"
    else
        echo "❌ 上传失败，响应内容："
        echo "$UPLOAD_RESPONSE"
        exit 1
    fi
fi

echo ""
echo "✅ 分发完成！"
echo ""
echo "📋 后续操作："
echo "1. 将下载链接和密码分享给测试人员"
echo "2. 测试人员在iOS设备上打开链接下载安装"
echo "3. 首次安装需要在设置中信任开发者证书"
echo ""
echo "💡 提示："
echo "- 可以设置环境变量 PGYER_API_KEY 避免每次输入"
echo "- 建议为不同版本设置不同的安装密码"
echo "- 可在蒲公英后台查看下载统计和崩溃日志"

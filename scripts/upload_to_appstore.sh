#!/bin/bash

# 卡通相机 App Store 上传脚本
# 此脚本用于将构建好的IPA文件上传到App Store Connect

set -e

echo "📤 卡通相机 App Store 上传工具"
echo "================================"

# 检查IPA文件是否存在
IPA_PATH="build/ios/ipa/cartoon_camera.ipa"
if [ ! -f "$IPA_PATH" ]; then
    echo "❌ 错误：未找到IPA文件"
    echo "请先运行构建脚本: ./scripts/build_release.sh"
    exit 1
fi

echo "✅ 找到IPA文件: $IPA_PATH"

# 检查文件大小
IPA_SIZE=$(du -h "$IPA_PATH" | cut -f1)
echo "📦 IPA文件大小: $IPA_SIZE"

echo ""
echo "🔧 上传方式选择："
echo "1. 使用Apple Transporter（推荐）"
echo "2. 使用命令行工具"
echo "3. 使用Xcode Organizer"
echo ""

read -p "请选择上传方式 (1-3): " choice

case $choice in
    1)
        echo ""
        echo "📱 使用Apple Transporter上传："
        echo "1. 从Mac App Store下载并打开Transporter应用"
        echo "2. 将以下文件拖拽到Transporter中："
        echo "   $PWD/$IPA_PATH"
        echo "3. 点击'交付'按钮完成上传"
        echo ""
        echo "🔗 打开IPA文件所在目录..."
        open "build/ios/ipa/"
        ;;
    2)
        echo ""
        echo "⚠️  使用命令行上传需要API密钥"
        echo "请确保您已经在App Store Connect中创建了API密钥"
        echo ""
        read -p "请输入您的API密钥ID: " api_key
        read -p "请输入您的发行商ID: " issuer_id
        read -p "请输入API密钥文件路径: " key_file
        
        if [ -z "$api_key" ] || [ -z "$issuer_id" ] || [ -z "$key_file" ]; then
            echo "❌ 错误：API信息不完整"
            exit 1
        fi
        
        echo ""
        echo "🚀 开始上传..."
        xcrun altool --upload-app --type ios \
            -f "$IPA_PATH" \
            --apiKey "$api_key" \
            --apiIssuer "$issuer_id" \
            --apiKeyFile "$key_file"
        
        if [ $? -eq 0 ]; then
            echo "✅ 上传成功！"
        else
            echo "❌ 上传失败，请检查API配置和网络连接"
            exit 1
        fi
        ;;
    3)
        echo ""
        echo "🔧 使用Xcode Organizer上传："
        echo "1. 正在打开Archive目录..."
        open "build/ios/archive/"
        echo ""
        echo "2. 双击.xcarchive文件打开Xcode Organizer"
        echo "3. 选择Archive并点击'Distribute App'"
        echo "4. 选择'App Store Connect'作为分发方式"
        echo "5. 按照向导完成上传"
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo ""
echo "📋 上传完成后的下一步："
echo "1. 等待App Store Connect处理构建版本（通常5-30分钟）"
echo "2. 在App Store Connect中选择构建版本"
echo "3. 完善应用信息和截图"
echo "4. 提交应用审核"
echo ""
echo "🔗 App Store Connect: https://appstoreconnect.apple.com/"
echo ""
echo "⏰ 预计审核时间：24-48小时"

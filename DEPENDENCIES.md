# 依赖说明文档

## AI 头像生成应用依赖优化

### ✅ 已移除的依赖

以下依赖已从原项目中移除，因为 AI 头像生成应用不需要：

- `camera: ^0.10.5+5` - 移除摄像头功能
- `just_audio: ^0.9.36` - 移除音频播放功能
- `just_audio_background: ^0.0.1-beta.11` - 移除后台音频
- `audio_service: ^0.18.12` - 移除音频服务
- `hive: ^2.2.3` - 移除复杂数据库，使用轻量级存储
- `hive_flutter: ^1.1.0` - 移除 Hive Flutter 支持
- `equatable: ^2.0.5` - 移除对象比较库（使用简单的==比较）
- `hive_generator: ^2.0.1` - 移除 Hive 代码生成

### ✅ 新增的 AI 头像生成专用依赖

#### 图片处理

- `image: ^4.1.3` - 图片处理和编辑库
- `image_picker: ^1.0.4` - 图片选择器（拍照/相册）

#### 分享和保存

- `share_plus: ^7.2.1` - 分享功能
- `gallery_saver: ^2.3.2` - 保存图片到相册

#### 网络请求

- `dio: ^5.4.0` - 强大的 HTTP 客户端（用于 AI API 调用）

#### JSON 序列化

- `json_annotation: ^4.8.1` - JSON 序列化注解

### ✅ 保留的核心依赖

#### 状态管理和 UI

- `get: ^4.6.6` - GetX 状态管理和路由
- `flutter_screenutil: ^5.9.0` - 响应式 UI 适配
- `cached_network_image: ^3.3.0` - 网络图片缓存
- `flutter_svg: ^2.0.9` - SVG 图片支持
- `lottie: ^2.7.0` - 动画支持

#### 系统功能

- `permission_handler: ^11.1.0` - 权限管理
- `path_provider: ^2.1.1` - 文件路径
- `path: ^1.8.3` - 路径工具

#### 存储和工具

- `get_storage: ^2.1.1` - 轻量级本地存储（已替换 SharedPreferences）
- `logger: ^2.0.2+1` - 日志记录
- `connectivity_plus: ^5.0.2` - 网络连接检测
- `device_info_plus: ^9.1.1` - 设备信息
- `package_info_plus: ^4.2.0` - 应用信息

#### 依赖注入

- 已迁移到 GetX Binding 统一管理

### 📱 AI 头像生成应用功能映射

| 功能         | 使用的依赖                                   |
| ------------ | -------------------------------------------- |
| 图片上传     | `image_picker`, `permission_handler`         |
| 图片处理     | `image`, `path_provider`                     |
| AI API 调用  | `dio`                                        |
| 结果展示     | `cached_network_image`, `flutter_screenutil` |
| 保存到相册   | `gallery_saver`, `permission_handler`        |
| 分享功能     | `share_plus`                                 |
| 用户数据存储 | `get_storage`                                |
| 状态管理     | `get`                                        |
| 网络检测     | `connectivity_plus`                          |
| 日志记录     | `logger`                                     |

### 🔧 开发依赖

- `flutter_lints: ^5.0.0` - 代码规范检查
- `build_runner: ^2.4.7` - 代码生成工具

- `json_serializable: ^6.7.1` - JSON 序列化代码生成

### 📝 注意事项

1. **轻量化原则**: 移除了所有不必要的依赖，减少应用体积
2. **功能专一**: 专注于 AI 头像生成功能
3. **性能优化**: 使用轻量级存储方案
4. **兼容性**: 所有依赖都是最新稳定版本
5. **扩展性**: 保留了核心架构，便于后续功能扩展
6. **避免冗余**: 移除了功能重复的依赖（如 http vs dio）
7. **保留必要功能**: 重新添加了 JSON 序列化依赖，用于 API 数据处理

### 🔍 为什么需要 JSON 序列化？

AI 头像生成应用中 JSON 序列化的重要用途：

1. **AI API 响应解析**: 处理 AI 服务返回的生成结果、错误信息等
2. **用户数据存储**: 用户信息、偏好设置的本地存储
3. **生成历史管理**: 头像生成记录的序列化存储
4. **风格配置**: 风格模板、参数配置的 JSON 处理
5. **会员信息**: 会员状态、套餐信息的数据处理

### 🚀 下一步

运行以下命令更新依赖：

```bash
flutter pub get
```

如果遇到依赖冲突，可以运行：

```bash
flutter pub deps
flutter pub upgrade
```

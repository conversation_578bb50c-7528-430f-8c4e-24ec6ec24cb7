# 图片缓存优化指南

## 概述

为了节省OSS资源和提升用户体验，项目已经实现了完整的图片缓存解决方案。同一个URL的图片资源不会重复下载，而是从本地缓存中读取。

## 核心组件

### 1. OptimizedNetworkImage 组件

这是项目中统一的网络图片组件，自动处理缓存、加载状态和错误处理。

```dart
OptimizedNetworkImage(
  imageUrl: 'https://example.com/image.jpg',
  width: 200.w,
  height: 200.h,
  fit: BoxFit.cover,
  borderRadius: BorderRadius.circular(12.r),
)
```

### 2. ImageCacheConfig 配置

自定义缓存管理器，提供以下功能：
- 缓存时间：7天
- 最大缓存文件数：200个
- 内存缓存：最多100张图片，最大50MB

## 缓存策略

### 磁盘缓存
- **缓存时间**：7天后自动清理
- **缓存位置**：应用私有目录
- **最大文件数**：200个文件
- **命名规则**：基于URL的哈希值

### 内存缓存
- **最大图片数**：100张
- **最大内存占用**：50MB
- **清理策略**：LRU（最近最少使用）

## 使用方法

### 基本使用

```dart
// ✅ 推荐：使用 OptimizedNetworkImage
OptimizedNetworkImage(
  imageUrl: imageUrl,
  width: 300.w,
  height: 200.h,
  fit: BoxFit.cover,
)

// ❌ 不推荐：直接使用 Image.network
Image.network(imageUrl) // 不会缓存
```

### 高级配置

```dart
OptimizedNetworkImage(
  imageUrl: imageUrl,
  width: 300.w,
  height: 200.h,
  fit: BoxFit.cover,
  borderRadius: BorderRadius.circular(12.r),
  placeholder: CustomLoadingWidget(), // 自定义加载组件
  errorWidget: CustomErrorWidget(),   // 自定义错误组件
  fadeInDuration: Duration(milliseconds: 500), // 淡入动画时长
)
```

## 缓存管理

### 清理缓存

```dart
// 清理所有缓存
await ImageCacheConfig.clearCache();
```

### 预加载图片

```dart
// 预加载单张图片
await ImageCacheConfig.preloadImage('https://example.com/image.jpg');

// 批量预加载
await ImageCacheConfig.preloadImages([
  'https://example.com/image1.jpg',
  'https://example.com/image2.jpg',
]);
```

## 性能优化

### 1. 图片尺寸优化
```dart
OptimizedNetworkImage(
  imageUrl: imageUrl,
  memCacheWidth: 300, // 内存缓存时的宽度
  memCacheHeight: 200, // 内存缓存时的高度
)
```

### 2. 渐进式加载
```dart
ProgressiveImage(
  imageUrl: highResImageUrl,
  thumbnailUrl: lowResImageUrl, // 先显示缩略图
  width: 300.w,
  height: 200.h,
)
```

## 已优化的组件

以下组件已经替换为使用缓存的版本：

- ✅ `ResultImageDisplay` - 结果页面图片展示
- ✅ `LatestCarousel` - 首页轮播图
- ✅ `GenerationPage` - 生成页面背景图
- ✅ `StyleCategorySection` - 风格分类卡片
- ✅ `VipPrivilegeCard` - VIP特权卡片图标
- ✅ `SettingsList` - 设置页面箭头图标

## 监控和调试

### 缓存状态监控
```dart
// 获取缓存大小（待实现）
final cacheSize = await ImageCacheConfig.getCacheSize();
print('当前缓存大小: ${cacheSize}MB');
```

### 调试模式
在开发模式下，可以通过日志查看缓存命中情况：
- 缓存命中：直接从缓存加载
- 缓存未命中：从网络下载并缓存

## 最佳实践

1. **统一使用 OptimizedNetworkImage**：不要直接使用 `Image.network`
2. **合理设置图片尺寸**：避免加载过大的图片到内存
3. **预加载关键图片**：在适当时机预加载用户可能查看的图片
4. **定期清理缓存**：在设置页面提供清理缓存的选项
5. **监控缓存大小**：避免缓存占用过多存储空间

## 注意事项

- 缓存文件存储在应用私有目录，卸载应用时会自动清理
- 网络异常时会自动使用缓存的图片
- 图片URL变化时会重新下载和缓存
- 内存不足时会自动清理最少使用的缓存图片

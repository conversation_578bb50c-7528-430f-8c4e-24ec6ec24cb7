PODS:
  - audio_service (0.0.1):
    - Flutter
    - FlutterMacOS
  - audio_session (0.0.1):
    - FlutterMacOS
  - connectivity_plus (0.0.1):
    - FlutterMacOS
    - ReachabilitySwift
  - device_info_plus (0.0.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - just_audio (0.0.1):
    - Flutter
    - FlutterMacOS
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - ReachabilitySwift (5.2.4)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - audio_service (from `Flutter/ephemeral/.symlinks/plugins/audio_service/darwin`)
  - audio_session (from `Flutter/ephemeral/.symlinks/plugins/audio_session/macos`)
  - connectivity_plus (from `Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos`)
  - device_info_plus (from `Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - just_audio (from `Flutter/ephemeral/.symlinks/plugins/just_audio/darwin`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `Flutter/ephemeral/.symlinks/plugins/sqflite_darwin/darwin`)

SPEC REPOS:
  trunk:
    - ReachabilitySwift

EXTERNAL SOURCES:
  audio_service:
    :path: Flutter/ephemeral/.symlinks/plugins/audio_service/darwin
  audio_session:
    :path: Flutter/ephemeral/.symlinks/plugins/audio_session/macos
  connectivity_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos
  device_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  just_audio:
    :path: Flutter/ephemeral/.symlinks/plugins/just_audio/darwin
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  sqflite_darwin:
    :path: Flutter/ephemeral/.symlinks/plugins/sqflite_darwin/darwin

SPEC CHECKSUMS:
  audio_service: aa99a6ba2ae7565996015322b0bb024e1d25c6fd
  audio_session: eaca2512cf2b39212d724f35d11f46180ad3a33e
  connectivity_plus: e74b9f74717d2d99d45751750e266e55912baeb5
  device_info_plus: b0fafc687fb901e2af612763340f1b0d4352f8e5
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  just_audio: 4e391f57b79cad2b0674030a00453ca5ce817eed
  package_info_plus: 122abb51244f66eead59ce7c9c200d6b53111779
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0

PODFILE CHECKSUM: 7eb978b976557c8c1cd717d8185ec483fd090a82

COCOAPODS: 1.16.2
